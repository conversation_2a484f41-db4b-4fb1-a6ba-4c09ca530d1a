//+------------------------------------------------------------------+
//|                                             SignalResult.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"

#ifndef ORTBO_SIGNAL_RESULT_MQH
#define ORTBO_SIGNAL_RESULT_MQH

// Assuming ORTBO_Enums.mqh is accessible.
// If ORTBO_Enums.mqh is in MQL5/Include/, use: #include <ORTBO_Enums.mqh>
// If ORTBO_Enums.mqh is in MQL5/Include/ORTBO/, use: #include <ORTBO/ORTBO_Enums.mqh>
// If this file (SignalResult.mqh) is in MQL5/Include/ORTBO/Signals/Base/
// and ORTBO_Enums.mqh is in MQL5/Include/ORTBO/, the path would be:
// #include "../../ORTBO_Enums.mqh" // For Direction, ENUM_TIMEFRAMES
#include <ORTBO/Enums/ORTBO_Enums.mqh>

//+------------------------------------------------------------------+
//| Structure to hold the rich result of a signal calculation.       |
//+------------------------------------------------------------------+
struct SignalResult
  {
   Direction         direction;                // Buy, Sell, or None from ORTBO_Enums.mqh
   double            strength;                 // Signal confidence (e.g., 0-100), interpretation specific to provider
   datetime          timestamp;                // Time the signal was calculated/generated (TimeCurrent())
   string            providerName;             // Name of the signal provider (e.g., "MA", "RSI")
   string            symbol;                   // Symbol for which the signal was generated
   ENUM_TIMEFRAMES   calculatedOnTimeframe;    // Timeframe used for the calculation by the provider
   double            primaryIndicatorValue;    // Key value from the indicator (e.g., MA value, RSI value)
   double            supportingPriceValue;     // Key price value used in calculation (e.g., close price)
   bool              isValid;                  // True if calculation was successful and result is meaningful
   string            errorMessage;             // Description of the error, if any occurred

//--- Constructor
               SignalResult()
                 {
                  Reset();
                 }

//--- Resets the structure to a default/invalid state
   void        Reset()
                 {
                  direction = None;
                  strength = 0.0;
                  timestamp = 0;
                  providerName = "";
                  symbol = "";
                  calculatedOnTimeframe = PERIOD_CURRENT; // Default, should be set by provider
                  primaryIndicatorValue = EMPTY_VALUE;    // MQL5 constant for uninitialized double
                  supportingPriceValue = EMPTY_VALUE;     // MQL5 constant
                  isValid = false;                        // Default to not valid until explicitly set
                  errorMessage = "";
                 }

//--- Helper to set error state
   void        SetError(const string msg)
                 {
                  // Keep symbol, providerName, calculatedOnTimeframe if already set by provider
                  // Reset other fields to indicate error
                  direction = None;
                  strength = 0.0;
                  primaryIndicatorValue = EMPTY_VALUE;
                  supportingPriceValue = EMPTY_VALUE;
                  
                  isValid = false;
                  errorMessage = msg;
                  timestamp = TimeCurrent(); // Mark error time
                 }

//--- Helper to set success state
   void        SetSuccess(Direction dir, double str = 100.0) // Default strength 100%
                 {
                  // Keep symbol, providerName, calculatedOnTimeframe if already set by provider
                  // Reset error message and set other fields
                  errorMessage = "";
                  
                  isValid = true;
                  direction = dir;
                  strength = str;
                  timestamp = TimeCurrent();
                  // primaryIndicatorValue and supportingPriceValue should be set by the provider before calling this
                 }
                 
//--- Provides a string representation for logging/debugging
   string      ToString() const
                 {
                  string str_result;
                  if(!isValid)
                     str_result = StringFormat("SignalResult[Symbol:%s Provider:%s TF:%s Status:INVALID Time:%s Error:'%s']",
                                         symbol,
                                         providerName,
                                         EnumToString(calculatedOnTimeframe),
                                         TimeToString(timestamp, TIME_SECONDS),
                                         errorMessage);
                  else
                     str_result = StringFormat("SignalResult[Symbol:%s Provider:%s TF:%s Dir:%s Str:%.1f IndV:%.5f PriceV:%.5f Time:%s]",
                                     symbol,
                                     providerName,
                                     EnumToString(calculatedOnTimeframe),
                                     EnumToString(direction),
                                     strength,
                                     primaryIndicatorValue,
                                     supportingPriceValue,
                                     TimeToString(timestamp, TIME_SECONDS));
                  return str_result;
                 }
  };

#endif // ORTBO_SIGNAL_RESULT_MQH
