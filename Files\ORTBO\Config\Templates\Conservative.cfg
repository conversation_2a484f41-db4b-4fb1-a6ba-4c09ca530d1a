# ORTBO Conservative Trading Configuration Template
# This template focuses on stability and risk management
# Generated: 2025-01-27
# Version: 1.0

[SIGNAL_SRC_MA]
# Format: enabled|timeframe|filterInverter|period|method|appliedPrice|shift|marginPips
# Conservative MA: Longer period, higher margin for stability
1|60|0|21|0|0|0|10

[SIGNAL_SRC_RSI]
# Format: enabled|timeframe|filterInverter|period|minimum|maximum
# Conservative RSI: Longer period, tighter levels to avoid false signals
1|240|0|21|25.0|75.0

[SIGNAL_SRC_CSI]
# Format: enabled|timeframe|filterInverter|maPeriod|maDelta
# Conservative CSI: Longer MA period for smoother signals
1|60|0|30|2

[SIGNAL_SRC_CFFP]
# Format: enabled|timeframe|filterInverter|fastMAPeriod|slowMAPeriod|maMethod|appliedPrice
# Conservative CFFP: Wider spread between fast/slow MA for confirmed trends
1|60|0|8|21|0|0

[SIGNAL_SRC_CMSM]
# Format: enabled|timeframe|filterInverter|tradeLevel|timeIndex
# Conservative CMSM: Lower trade level for stronger signals only
1|240|0|1.5|0

# Configuration Notes:
# - All timeframes set to higher values for stability
# - RSI levels tightened to 25/75 to avoid ranging markets
# - MA periods increased for smoother signals
# - CFFP spread widened for trend confirmation
# - CMSM trade level lowered for quality signals
# - Margin pips increased for MA to avoid noise
