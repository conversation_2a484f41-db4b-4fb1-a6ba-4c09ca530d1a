//+------------------------------------------------------------------+
//|                                                  CCFFPConfig.mqh |
//|                                      Copyright 2025, Your Name   |
//|                                     https://www.yourwebsite.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name"
#property link      "https://www.yourwebsite.com"
#property version   "1.00"

#ifndef ORTBO_CCFFP_CONFIG_MQH
#define ORTBO_CCFFP_CONFIG_MQH

#include "CProviderConfigBase.mqh"

//+------------------------------------------------------------------+
//| CFFP provider configuration                                      |
//+------------------------------------------------------------------+
class CCFFPConfig : public CProviderConfigBase
  {
private:
   int               m_fastMAPeriod;      // Fast MA period
   int               m_slowMAPeriod;      // Slow MA period
   ENUM_MA_METHOD    m_maMethod;          // MA method
   ENUM_APPLIED_PRICE m_appliedPrice;     // Applied price
   
public:
   //--- Constructor with default values
                     CCFFPConfig() : m_fastMAPeriod(3),
                                     m_slowMAPeriod(5),
                                     m_maMethod(MODE_SMA),
                                     m_appliedPrice(PRICE_CLOSE)
     {
      m_configType = "CFFP";
     }
   
   //--- Constructor with parameters
                     CCFFPConfig(int fastMAPeriod,
                                 int slowMAPeriod,
                                 ENUM_MA_METHOD maMethod,
                                 ENUM_APPLIED_PRICE appliedPrice,
                                 bool filterInverter,
                                 ENUM_TIMEFRAMES timeframe)
     {
      m_configType = "CFFP";
      m_fastMAPeriod = fastMAPeriod;
      m_slowMAPeriod = slowMAPeriod;
      m_maMethod = maMethod;
      m_appliedPrice = appliedPrice;
      m_filterInverter = filterInverter;
      m_timeframe = timeframe;
      m_enabled = true;
     }
   
   //--- Validation
   virtual bool      Validate() override
     {
      string error;
      
      // Validate common parameters
      if(!ValidateCommon(error))
        {
         SetValidationError(error);
         return false;
        }
      
      // Validate fast MA period
      if(!CConfigValidator::ValidateIntRange(m_fastMAPeriod, 1, 200, error))
        {
         SetValidationError("CFFP Fast MA Period: " + error);
         return false;
        }
      
      // Validate slow MA period
      if(!CConfigValidator::ValidateIntRange(m_slowMAPeriod, 1, 200, error))
        {
         SetValidationError("CFFP Slow MA Period: " + error);
         return false;
        }
      
      // Validate that fast < slow
      if(m_fastMAPeriod >= m_slowMAPeriod)
        {
         SetValidationError("CFFP Fast MA Period must be less than Slow MA Period");
         return false;
        }
      
      // Validate MA method
      if(!CConfigValidator::ValidateMAMethod(m_maMethod, error))
        {
         SetValidationError("CFFP MA Method: " + error);
         return false;
        }
      
      // Validate applied price
      if(!CConfigValidator::ValidateAppliedPrice(m_appliedPrice, error))
        {
         SetValidationError("CFFP Applied Price: " + error);
         return false;
        }
      
      ClearValidationError();
      return true;
     }
   
   //--- Serialization
   virtual bool      LoadFromString(const string configString) override
     {
      string params[];
      int count = StringSplit(configString, '|', params);
      
      if(count < 7)
        {
         SetValidationError("Invalid configuration string format");
         return false;
        }
      
      int index = 0;
      
      // Parse common parameters
      if(!ParseCommonParams(params, index))
        {
         SetValidationError("Failed to parse common parameters");
         return false;
        }
      
      // Parse CFFP-specific parameters
      m_fastMAPeriod = (int)StringToInteger(params[index++]);
      m_slowMAPeriod = (int)StringToInteger(params[index++]);
      m_maMethod = (ENUM_MA_METHOD)StringToInteger(params[index++]);
      m_appliedPrice = (ENUM_APPLIED_PRICE)StringToInteger(params[index++]);
      
      return Validate();
     }
   
   virtual string    SaveToString() const override
     {
      string common = CommonParamsToString();
      string specific = StringFormat("%d|%d|%d|%d", 
                                     m_fastMAPeriod, m_slowMAPeriod, 
                                     m_maMethod, m_appliedPrice);
      return common + "|" + specific;
     }
   
   //--- Clone
   virtual CConfigBase* Clone() const override
     {
      CCFFPConfig* clone = new CCFFPConfig();
      clone.m_enabled = m_enabled;
      clone.m_timeframe = m_timeframe;
      clone.m_filterInverter = m_filterInverter;
      clone.m_fastMAPeriod = m_fastMAPeriod;
      clone.m_slowMAPeriod = m_slowMAPeriod;
      clone.m_maMethod = m_maMethod;
      clone.m_appliedPrice = m_appliedPrice;
      clone.m_configName = m_configName;
      clone.m_version = m_version;
      return clone;
     }
   
   //--- Getters
   int               GetFastMAPeriod() const { return m_fastMAPeriod; }
   int               GetSlowMAPeriod() const { return m_slowMAPeriod; }
   ENUM_MA_METHOD    GetMAMethod() const { return m_maMethod; }
   ENUM_APPLIED_PRICE GetAppliedPrice() const { return m_appliedPrice; }
   
   //--- Setters
   void              SetFastMAPeriod(int period) 
     { 
      m_fastMAPeriod = period; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetSlowMAPeriod(int period) 
     { 
      m_slowMAPeriod = period; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetMAMethod(ENUM_MA_METHOD method) 
     { 
      m_maMethod = method; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetAppliedPrice(ENUM_APPLIED_PRICE price) 
     { 
      m_appliedPrice = price; 
      m_lastModified = TimeCurrent();
     }
  };

#endif // ORTBO_CCFFP_CONFIG_MQH