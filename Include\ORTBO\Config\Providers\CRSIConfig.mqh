//+------------------------------------------------------------------+
//|                                                   CRSIConfig.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"
#property version   "1.00"

#ifndef ORTBO_CRSI_CONFIG_MQH
#define ORTBO_CRSI_CONFIG_MQH

#include "CProviderConfigBase.mqh"

//+------------------------------------------------------------------+
//| RSI provider configuration                                       |
//+------------------------------------------------------------------+
class CRSIConfig : public CProviderConfigBase
  {
private:
   int               m_period;            // RSI period
   double            m_minimum;           // RSI minimum (oversold)
   double            m_maximum;           // RSI maximum (overbought)
   
public:
   //--- Constructor with default values
                     CRSIConfig() : m_period(14),
                                    m_minimum(30.0),
                                    m_maximum(70.0)
     {
      m_configType = "RSI";
     }
   
   //--- Constructor with parameters
                     CRSIConfig(int period,
                                double minimum,
                                double maximum,
                                bool filterInverter,
                                ENUM_TIMEFRAMES timeframe)
     {
      m_configType = "RSI";
      m_period = period;
      m_minimum = minimum;
      m_maximum = maximum;
      m_filterInverter = filterInverter;
      m_timeframe = timeframe;
      m_enabled = true;
     }
   
   //--- Validation
   virtual bool      Validate() override
     {
      string error;
      
      // Validate common parameters
      if(!ValidateCommon(error))
        {
         SetValidationError(error);
         return false;
        }
      
      // Validate RSI period
      if(!CConfigValidator::ValidateIntRange(m_period, 2, 1000, error))
        {
         SetValidationError("RSI Period: " + error);
         return false;
        }
      
      // Validate RSI levels
      if(!CConfigValidator::ValidateDoubleRange(m_minimum, 0.0, 100.0, error))
        {
         SetValidationError("RSI Minimum: " + error);
         return false;
        }
      
      if(!CConfigValidator::ValidateDoubleRange(m_maximum, 0.0, 100.0, error))
        {
         SetValidationError("RSI Maximum: " + error);
         return false;
        }
      
      // Validate that minimum < maximum
      if(m_minimum >= m_maximum)
        {
         SetValidationError("RSI Minimum must be less than Maximum");
         return false;
        }
      
      ClearValidationError();
      return true;
     }
   
   //--- Serialization
   virtual bool      LoadFromString(const string configString) override
     {
      string params[];
      int count = StringSplit(configString, '|', params);
      
      if(count < 6)
        {
         SetValidationError("Invalid configuration string format");
         return false;
        }
      
      int index = 0;
      
      // Parse common parameters
      if(!ParseCommonParams(params, index))
        {
         SetValidationError("Failed to parse common parameters");
         return false;
        }
      
      // Parse RSI-specific parameters
      m_period = (int)StringToInteger(params[index++]);
      m_minimum = StringToDouble(params[index++]);
      m_maximum = StringToDouble(params[index++]);
      
      return Validate();
     }
   
   virtual string    SaveToString() const override
     {
      string common = CommonParamsToString();
      string specific = StringFormat("%d|%.2f|%.2f", m_period, m_minimum, m_maximum);
      return common + "|" + specific;
     }
   
   //--- Clone
   virtual CConfigBase* Clone() const override
     {
      CRSIConfig* clone = new CRSIConfig();
      clone.m_enabled = m_enabled;
      clone.m_timeframe = m_timeframe;
      clone.m_filterInverter = m_filterInverter;
      clone.m_period = m_period;
      clone.m_minimum = m_minimum;
      clone.m_maximum = m_maximum;
      clone.m_configName = m_configName;
      clone.m_version = m_version;
      return clone;
     }
   
   //--- Getters
   int               GetPeriod() const { return m_period; }
   double            GetMinimum() const { return m_minimum; }
   double            GetMaximum() const { return m_maximum; }
   
   //--- Setters
   void              SetPeriod(int period) 
     { 
      m_period = period; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetMinimum(double minimum) 
     { 
      m_minimum = minimum; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetMaximum(double maximum)
     {
      m_maximum = maximum;
      m_lastModified = TimeCurrent();
     }

   //--- Binary serialization
   virtual bool      Serialize(int handle) override
     {
      if(!CProviderConfigBase::Serialize(handle))
         return false;

      FileWriteInteger(handle, m_period);
      FileWriteDouble(handle, m_minimum);
      FileWriteDouble(handle, m_maximum);

      return true;
     }

   virtual bool      Deserialize(int handle) override
     {
      if(!CProviderConfigBase::Deserialize(handle))
         return false;

      m_period = FileReadInteger(handle);
      m_minimum = FileReadDouble(handle);
      m_maximum = FileReadDouble(handle);

      return Validate();
     }
  };

#endif // ORTBO_CRSI_CONFIG_MQH