//+------------------------------------------------------------------+
//|                                                        ORTBO.mq5 |
//|                                       Copyright 2025, philuk |
//|                                      mailto:<EMAIL> |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, philuk"
#property link      "mailto:<EMAIL>"
#property version   "1.13" // Updated version for Phase 2 - MQL5 Market compatible format
#property strict

// --- Resource Embedding ---
#resource "CURRENCY_STRENGTH_INDEX.ex5"
#resource "CCFp.ex5"
#resource "\\Indicators\\###CMSMIndV17.06 (1).ex5"
#resource "\\Indicators\\###CMSMIndV24.01 (1).ex5"
// ... (ensure all your existing #resource lines are present) ...

#define EA_VERSION "1.13a_SignalRefactorP2" // Updated version

// --- Includes ---
#include <ORTBO_Enums.mqh>      // Existing enums (MUST CONTAIN 'Direction' enum etc)
#include <EquityMonitor.mqh>    // Your existing include
#include <GridEngine.mqh>       // Your existing include
#include <Display.mqh>       // Assuming Display is part of GridEngine or included elsewhere if needed
#include <Trade\PositionInfo.mqh> // For CPositionInfo in ProcessGridLogic

// +++ NEW: Include for the entire Signal System +++
// This single include brings in CSignalManager, CProviderFactory, and all providers.
#include <ORTBO/Signals/SignalIntegration.mqh>

// --- Define Number of Grid Strategies Per Symbol ---
#define NUMBER_OF_GRIDS_PER_SYMBOL 3 // Your existing define

// --- Inputs ---
// ALL your existing input parameters MUST be here.
// The CProviderFactory will use these global inputs.
input group "Main"
input MULTISYMBOL InputMultiSymbol = Current; 
input string      AllTradableSymbols = "EURUSD|GBPUSD|USDJPY|AUDUSD|USDCAD|NZDUSD|USDCHF"; 
input ulong       InpMagicMach1 = 1100; 
input ulong       InpMagicMach2 = 2200; 
input ulong       InpMagicMach3 = 3300; 

input group "MACH1 Signal Configuration" // Example
input ENUM_SIGNAL_SOURCE InpMach1TriggerSource = SIGNAL_SRC_MA_AND_RSI; 
input ENUM_SIGNAL_SOURCE InpMach1Strat1Source  = SIGNAL_SRC_CSI;  
input ENUM_SIGNAL_SOURCE InpMach1Strat2Source  = SIGNAL_SRC_NONE; 
input ENUM_GRID_DIRECTION InpMach1Direction    = GRID_DIR_BUY_ONLY; 

input group "MACH2 Signal Configuration"
input ENUM_SIGNAL_SOURCE InpMach2TriggerSource = SIGNAL_SRC_MA;  
input ENUM_SIGNAL_SOURCE InpMach2Strat1Source  = SIGNAL_SRC_RSI; 
input ENUM_SIGNAL_SOURCE InpMach2Strat2Source  = SIGNAL_SRC_CSI; 
input ENUM_GRID_DIRECTION InpMach2Direction    = GRID_DIR_BOTH; 

input group "MACH3 Signal Configuration"
input ENUM_SIGNAL_SOURCE InpMach3TriggerSource = SIGNAL_SRC_RSI; 
input ENUM_SIGNAL_SOURCE InpMach3Strat1Source  = SIGNAL_SRC_MA;  
input ENUM_SIGNAL_SOURCE InpMach3Strat2Source  = SIGNAL_SRC_NONE;
input ENUM_GRID_DIRECTION InpMach3Direction    = GRID_DIR_BOTH;

input group "Moving Average (MA) Signal"
input bool             InpEnableSignalMovingAverage = true; 
input bool             InpMaFilterInverter    = false; 
input ENUM_TIMEFRAMES  InpMaFrame             = PERIOD_M1; 
input int              InpMaPeriod            = 3; 
input ENUM_MA_METHOD   InpMaMethod            = MODE_EMA; 
input ENUM_APPLIED_PRICE InpMaPrice           = PRICE_CLOSE; 
input int              InpMaShift             = 0; 
input int              InpMaMargin            = 6;

input group "Relative Strength Index (RSI) Signal"
input bool             InpEnableSignalRSI     = true; 
input bool             InpRSIFilterInverter   = false; 
input ENUM_TIMEFRAMES  InpRSIFrame            = PERIOD_H1; 
input int              InpRsiPeriod           = 14; 
input double           InpRsiMinimum          = 30.0; 
input double           InpRsiMaximum          = 70.0; 

input group "Currency Strength Index (CSI) Signal"
input bool             InpEnableSignalCSI     = true; 
input ENUM_TIMEFRAMES  InpCSIFrame            = PERIOD_CURRENT;
input int              InpCSIMAPeriod         = 20; 
input int              InpCSIMADelta          = 1; 
input bool             InpCSIFilterInverter   = false; 

input group "Currency Strength (CFFP) Signal"
input bool             InpEnableSignalCFFP    = true;       
input ENUM_TIMEFRAMES  InpCFFPFrame           = PERIOD_CURRENT;
input int              InpCFFPFastMAPeriod    = 3;          
input int              InpCFFPSlowMAPeriod    = 5;          
input ENUM_MA_METHOD   InpCFFPMAMethod        = MODE_SMA;     
input ENUM_APPLIED_PRICE InpCFFPAppliedPrice  = PRICE_CLOSE;  
input bool             InpCFFPFilterInverter  = false;      

input group "Currency Momentom Strength Meter (CMSM17) Signal";
input bool             InpEnableSignalCMSM    = true;  
input bool             InpCMSMFilterInverter  = false; 
input ENUM_TIMEFRAMES  InpCMSMTimeFrame       = PERIOD_W1; // Timeframe for CMSM indicator
input double           InpCMSM_TradeLevel     = 2.0;     // Example param for CMSM iCustom
input int              InpCMSM_TimeIndex      = 0;       // Example param for CMSM iCustom
// IMPORTANT: Add ALL other input parameters that your CMSM indicator's iCustom call requires.
// These need to be declared as 'extern' in CProviderFactory.mqh.

// --- Include EquityMonitor Inputs ---
input group "" // Separator
// EquityMonitor inputs are defined within EquityMonitor.mqh

// --- Include GridEngine Inputs ---
input group "" // Separator
// GridEngine inputs are defined within GridEngine.mqh
// Note: GridEngine now includes inputs for Initial Lot Mode, Risk%, SL Points, Grid Width Mode.

// --- Global Objects ---
EquityMonitor g_equityMonitor;    // Your existing object
GridEngine    g_gridEngine;       // Your existing object
GridInstance* g_gridInstances[];  // Your existing array
Display       g_display;          // Your existing object

// CSignalManager* g_theGlobalSignalManager is now declared in SignalIntegration.mqh

// --- Multi-Symbol Globals ---
string SymbolArray[]; // Will be populated in OnInit
int    NumberOfTradeableSymbols = 0; // Will be set in OnInit

// --- REMOVE Old Indicator Handles, Buffers, and Optimization Arrays ---
// These are now managed within their respective provider classes.
// int m_MAHandle[];
// int m_RSIHandle[];
// int m_csiHandle, m_cffpHandle, m_cmsmHandle;
// double m_MABuffer[], m_RSIBuffer[];
// datetime m_MALastBarTime[], m_RSILastBarTime[], m_CSILastBarTime[], etc.
// Direction m_MALastSignal[], m_RSILastSignal[], m_CSILastSignal[], etc.
// string m_CurrencyNames[], m_CFFPCurrencyNames[]; // These are now internal to providers
// int m_BaseCurrencyIndex[], m_QuoteCurrencyIndex[], m_CFFPBaseCurrencyIndex[], m_CFFPQuoteCurrencyIndex[];


//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
   PrintFormat("ORTBO v%s – OnInit Start.", EA_VERSION);

   // --- Setup Multi-Symbol Array ---
   PrintFormat("ORTBO v%s – Step 2: Setting up Multi-Symbol Array...", EA_VERSION);
   if(InputMultiSymbol == Current)
     {
      NumberOfTradeableSymbols = 1;
      ArrayResize(SymbolArray, NumberOfTradeableSymbols);
      SymbolArray[0] = _Symbol;
     }
   else // All
     {
      NumberOfTradeableSymbols = StringSplit(AllTradableSymbols, '|', SymbolArray);
      if(NumberOfTradeableSymbols <= 0)
        { PrintFormat("[ERROR] ORTBO: No valid symbols in AllTradableSymbols: '%s'.", AllTradableSymbols); return INIT_FAILED; }
     }
   PrintFormat("ORTBO: Trading %d symbols.", NumberOfTradeableSymbols);
   for(int i=0; i<NumberOfTradeableSymbols; i++) // Ensure symbols are selected
     {
      if(!SymbolSelect(SymbolArray[i], true)) { /* Log error */ }
      MqlTick tick; if(!SymbolInfoTick(SymbolArray[i],tick)) { /* Log warning */ }
     }
   PrintFormat("ORTBO v%s – Step 3: Multi-Symbol Setup Complete.", EA_VERSION);

   // --- Magic Number Overlap Check ---
   PrintFormat("ORTBO v%s – Step 4: Checking Magic Numbers...", EA_VERSION);
   ulong maxMagic1 = InpMagicMach1 + NumberOfTradeableSymbols;
   ulong maxMagic2 = InpMagicMach2 + NumberOfTradeableSymbols;
   ulong maxMagic3 = InpMagicMach3 + NumberOfTradeableSymbols;

   if ((InpMagicMach1 < maxMagic2 && maxMagic1 > InpMagicMach2) ||
       (InpMagicMach2 < maxMagic1 && maxMagic2 > InpMagicMach1))
   {
       PrintFormat("[ERROR] ORTBO: Magic number ranges overlap! MACH1 (%d-%d) and MACH2 (%d-%d)",
                   InpMagicMach1 + 1, maxMagic1, InpMagicMach2 + 1, maxMagic2);
       return INIT_FAILED;
   }
   if ((InpMagicMach1 < maxMagic3 && maxMagic1 > InpMagicMach3) ||
       (InpMagicMach3 < maxMagic1 && maxMagic3 > InpMagicMach1))
   {
       PrintFormat("[ERROR] ORTBO: Magic number ranges overlap! MACH1 (%d-%d) and MACH3 (%d-%d)",
                   InpMagicMach1 + 1, maxMagic1, InpMagicMach3 + 1, maxMagic3);
       return INIT_FAILED;
   }
   if ((InpMagicMach2 < maxMagic3 && maxMagic2 > InpMagicMach3) ||
       (InpMagicMach3 < maxMagic2 && maxMagic3 > InpMagicMach2))
   {
       PrintFormat("[ERROR] ORTBO: Magic number ranges overlap! MACH2 (%d-%d) and MACH3 (%d-%d)",
                   InpMagicMach2 + 1, maxMagic2, InpMagicMach3 + 1, maxMagic3);
       return INIT_FAILED;
   }
   PrintFormat("ORTBO v%s – Step 5: Magic Number Check Complete.", EA_VERSION);

   // --- CORRECTED ORDER: Initialize Modules BEFORE Display Creation ---

   PrintFormat("ORTBO v%s – Step 6: Initializing EquityMonitor...", EA_VERSION);
   // --- Initialise EquityMonitor ---
   int init = g_equityMonitor.OnInit(GetPointer(g_display));
   if (init != INIT_SUCCEEDED)
   {
      PrintFormat("[ERROR] ORTBO: EquityMonitor initialization failed. Code: %d", init);
      return init;
   }
   Print("ORTBO: EquityMonitor Initialized.");
   PrintFormat("ORTBO v%s – Step 7: EquityMonitor Initialized OK.", EA_VERSION);

   PrintFormat("ORTBO v%s – Step 8: Creating Grid Instances...", EA_VERSION);
   // --- Dynamically create grids ---
   int totalGrids = NumberOfTradeableSymbols * NUMBER_OF_GRIDS_PER_SYMBOL;
   ArrayResize(g_gridInstances, totalGrids);
   for(int i = 0; i < NumberOfTradeableSymbols; i++)
   {
      string currentSymbol = SymbolArray[i];

      // --- Create MACH1 Grid ---
      ulong magic1 = InpMagicMach1 + (i + 1);
      string name1 = "MACH1_" + currentSymbol;
      int index1 = i * NUMBER_OF_GRIDS_PER_SYMBOL + 0;
      g_gridInstances[index1] = g_gridEngine.CreateGrid(name1, magic1, currentSymbol);
      if(g_gridInstances[index1] == NULL)
      {
         PrintFormat("[ERROR] ORTBO: Failed to create grid instance %s", name1);
         return INIT_FAILED;
      }
      g_gridInstances[index1].SetRiskParameters(InpInitialLotMode, InpRiskPercent, InpInitialSLPoints);
      g_gridInstances[index1].SetGridWidthParameters(InpGridWidthMode);
      g_gridInstances[index1].SetSignalConfiguration(InpMach1TriggerSource, InpMach1Strat1Source, InpMach1Strat2Source, InpMach1Direction);

      // --- Create MACH2 Grid ---
      ulong magic2 = InpMagicMach2 + (i + 1);
      string name2 = "MACH2_" + currentSymbol;
      int index2 = i * NUMBER_OF_GRIDS_PER_SYMBOL + 1;
      g_gridInstances[index2] = g_gridEngine.CreateGrid(name2, magic2, currentSymbol);
      if(g_gridInstances[index2] == NULL)
      {
         PrintFormat("[ERROR] ORTBO: Failed to create grid instance %s", name2);
         return INIT_FAILED;
      }
      g_gridInstances[index2].SetRiskParameters(InpInitialLotMode, InpRiskPercent, InpInitialSLPoints);
      g_gridInstances[index2].SetGridWidthParameters(InpGridWidthMode);
      g_gridInstances[index2].SetSignalConfiguration(InpMach2TriggerSource, InpMach2Strat1Source, InpMach2Strat2Source, InpMach2Direction);

      // --- Create MACH3 Grid ---
      ulong magic3 = InpMagicMach3 + (i + 1);
      string name3 = "MACH3_" + currentSymbol;
      int index3 = i * NUMBER_OF_GRIDS_PER_SYMBOL + 2;
      g_gridInstances[index3] = g_gridEngine.CreateGrid(name3, magic3, currentSymbol);
      if(g_gridInstances[index3] == NULL)
      {
         PrintFormat("[ERROR] ORTBO: Failed to create grid instance %s", name3);
         return INIT_FAILED;
      }
      g_gridInstances[index3].SetRiskParameters(InpInitialLotMode, InpRiskPercent, InpInitialSLPoints);
      g_gridInstances[index3].SetGridWidthParameters(InpGridWidthMode);
      g_gridInstances[index3].SetSignalConfiguration(InpMach3TriggerSource, InpMach3Strat1Source, InpMach3Strat2Source, InpMach3Direction);
   }
   PrintFormat("ORTBO v%s – Step 9: Grid Instances Created OK.", EA_VERSION);

   PrintFormat("ORTBO v%s – Step 10: Initializing GridEngine...", EA_VERSION);
   // --- Initialise GridEngine ---
   init = g_gridEngine.OnInit(GetPointer(g_display));
   if (init != INIT_SUCCEEDED)
   {
      PrintFormat("[ERROR] ORTBO: GridEngine initialization failed. Code: %d", init);
      return init;
   }
   Print("ORTBO: GridEngine Initialized.");
   PrintFormat("ORTBO v%s – Step 11: GridEngine Initialized OK.", EA_VERSION);

   // --- CORRECTED ORDER: Now Initialize Display ---

   PrintFormat("ORTBO v%s – Step 12: Calling CreateCommonDisplay()...", EA_VERSION);
   // --- Initialise Common Display ---
   init = CreateCommonDisplay();
   if (init != INIT_SUCCEEDED)
   {
       PrintFormat("ORTBO v%s – Step 12 FAILED: CreateCommonDisplay() returned error code %d.", EA_VERSION, init);
       return init;
   }
   PrintFormat("ORTBO v%s – Step 13: CreateCommonDisplay() Succeeded.", EA_VERSION);

   // --- Display Pointer Check ---
   PrintFormat("ORTBO v%s – Step 14: Checking Display Pointer...", EA_VERSION);
   if(CheckPointer(GetPointer(g_display)) == POINTER_INVALID)
   {
      PrintFormat("[ERROR] ORTBO: Display object pointer is invalid after CreateCommonDisplay.");
      return INIT_FAILED;
   }
   Print("ORTBO: Common Display Initialized.");
   PrintFormat("ORTBO v%s – Step 15: Display Pointer OK.", EA_VERSION);

   // +++ NEW: Initialize the Signal System +++
   PrintFormat("ORTBO v%s – Step 16: Initializing Signal System...", EA_VERSION);
   if(ORTBO_InitializeSignalSystem() != INIT_SUCCEEDED) // This function is in SignalIntegration.mqh
     {
      PrintFormat("[ERROR] ORTBO: Failed to initialize the new Signal System. EA exiting.");
      // Consider deinitializing other modules if necessary before exiting
      return INIT_FAILED;
     }
   Print("ORTBO: Signal System Initialized successfully.");
   PrintFormat("ORTBO v%s – Step 17: Signal System Initialized OK.", EA_VERSION);

   // --- REMOVE Old Indicator Initialization ---
   // The InitRemainingIndicators() function and its contents (InitRSI, iCustom calls for CSI/CFFP/CMSM,
   // currency mapping calls like MapCurrencyIndices) are no longer needed here.
   // All provider creation and initialization is handled by ORTBO_InitializeSignalSystem().

   PrintFormat("ORTBO v%s: OnInit Initialization complete.", EA_VERSION);
   return INIT_SUCCEEDED;
  }

//+------------------------------------------------------------------+
//| Create the common display panel                                  |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Create the common display panel                                  |
//+------------------------------------------------------------------+
int CreateCommonDisplay()
{
  PrintFormat("ORTBO: Entering CreateCommonDisplay()...");

  PrintFormat("ORTBO: Calling g_equityMonitor.Rows()...");
  int eqRows = g_equityMonitor.Rows();
  PrintFormat("ORTBO: g_equityMonitor.Rows() returned %d.", eqRows);
  PrintFormat("ORTBO: Calling g_gridEngine.Rows()...");
  int geRows = g_gridEngine.Rows();
  PrintFormat("ORTBO: g_gridEngine.Rows() returned %d.", geRows);

  PrintFormat("ORTBO: Calculating total rows...");
  int rows = 1 + eqRows + 1 + 1 + geRows;
  PrintFormat("ORTBO: Total display rows calculated: %d", rows);

  PrintFormat("ORTBO: Calling g_display.Create()...");
  // Create the main dialog window
  if (!g_display.Create("ORTBO v" + EA_VERSION, 2, rows, 100))
  {
    Print("[ERROR] ORTBO: Could not initialize display dialog (g_display.Create failed).");
    return INIT_FAILED;
  }
  PrintFormat("ORTBO: g_display.Create() succeeded.");

  int row = 0;
  // --- Create Equity Monitor section ---
  PrintFormat("ORTBO: Creating EquityMonitor title row...");
  if (!g_display.CreateRow("EquityMonitor", g_equityMonitor.Name(), row, 8, clrGold))
  {
     Print("[ERROR] ORTBO: Failed to create EquityMonitor title row in display.");
     return INIT_FAILED;
  }
  row++;
  PrintFormat("ORTBO: Creating EquityMonitor content rows...");
  if (!g_equityMonitor.CreateRows(row))
  {
     Print("[ERROR] ORTBO: Failed to create EquityMonitor content rows.");
     return INIT_FAILED;
  }
  row += eqRows;
  row++; // Gap

  // --- Create Grid Engine section ---
  PrintFormat("ORTBO: Creating GridEngine title row...");
  if (!g_display.CreateRow("GridEngine", g_gridEngine.Name(), row, 8, clrGold))
  {
     Print("[ERROR] ORTBO: Failed to create GridEngine title row in display.");
     return INIT_FAILED;
  }
  row++;
  PrintFormat("ORTBO: Creating GridEngine content rows...");
  if (!g_gridEngine.CreateRows(row))
  {
     Print("[ERROR] ORTBO: Failed to create GridEngine content rows.");
     return INIT_FAILED;
  }
  // row += geRows; // GridEngine manages its own row count internally now

  PrintFormat("ORTBO: Calling g_display.Run()...");
  // Start the display
  if (!g_display.Run())
  {
    Print("[ERROR] ORTBO: Could not start (run) the display dialog (g_display.Run failed).");
    return INIT_FAILED;
  }
  PrintFormat("ORTBO: g_display.Run() succeeded. Exiting CreateCommonDisplay() successfully.");

  return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   PrintFormat("ORTBO: Deinitializing... Reason: %d", reason);

   // +++ NEW: Deinitialize the Signal System +++
   ORTBO_DeinitializeSignalSystem(); // This function is in SignalIntegration.mqh

   // --- REMOVE Old Indicator Handle Release Logic ---
   // Indicator handles are now managed by their respective provider classes.
   // The DeinitializeManager call above ensures providers release their handles.
   // Example: (No longer needed)
   // for(int i = 0; i < NumberOfTradeableSymbols; i++) { if(m_RSIHandle[i]!=INVALID_HANDLE) IndicatorRelease(m_RSIHandle[i]); }
   // if(m_csiHandle != INVALID_HANDLE) IndicatorRelease(m_csiHandle);
   
   // Deinit modules (GridEngine first, then EquityMonitor, then Display)
   g_gridEngine.OnDeinit(reason);
   g_equityMonitor.OnDeinit(reason);
   if(CheckPointer(GetPointer(g_display)) != POINTER_INVALID)
   {
     g_display.Destroy(reason);
     Print("ORTBO: Common Display destroyed.");
   }

   Print("ORTBO: Deinitialization complete.");
  }

//+------------------------------------------------------------------+
//| Process Grid Logic Function Declaration                          |
//+------------------------------------------------------------------+
void ProcessGridLogic(GridInstance* gridInstance, Direction sigMA, Direction sigRSI, Direction sigCSI, Direction sigCFFP, Direction sigCMSM);

//+------------------------------------------------------------------+
//| Symbol Index By Symbol Function Declaration                      |
//+------------------------------------------------------------------+
int SymbolIndexBySymbol(const string symbol);

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
  {
   // --- Main Multi-Symbol Loop ---
   for(int SymbolLoop = 0; SymbolLoop < NumberOfTradeableSymbols; SymbolLoop++)
     {
      string CurrentSymbol = SymbolArray[SymbolLoop];
      
      // --- Signal Fetching is now centralized in GetSignal() called by ProcessGridLogic ---
      // No need to pre-fetch signalMA, signalRSI etc. here directly for ProcessGridLogic.
      // ProcessGridLogic will call GetSignal(SymbolLoop, configuredSource) which routes to the new system.

      // --- Calculate Indices for this symbol's grids ---
      int indexMach1 = SymbolLoop * NUMBER_OF_GRIDS_PER_SYMBOL + 0;
      int indexMach2 = SymbolLoop * NUMBER_OF_GRIDS_PER_SYMBOL + 1;
      int indexMach3 = SymbolLoop * NUMBER_OF_GRIDS_PER_SYMBOL + 2;

      // --- Get Grid Pointers for this symbol ---
      GridInstance* targetGrid1 = (ArraySize(g_gridInstances) > indexMach1) ? g_gridInstances[indexMach1] : NULL;
      GridInstance* targetGrid2 = (ArraySize(g_gridInstances) > indexMach2) ? g_gridInstances[indexMach2] : NULL;
      GridInstance* targetGrid3 = (ArraySize(g_gridInstances) > indexMach3) ? g_gridInstances[indexMach3] : NULL;

      // --- Call ProcessGridLogic for each grid ---
      // ProcessGridLogic parameters (sigMA, sigRSI, etc.) are now DUMMY if ProcessGridLogic
      // is modified to call GetSignal itself.
      // OR, if ProcessGridLogic still EXPECTS these, we must fetch them here using GetSignal.
      // Let's assume ProcessGridLogic still expects them for minimal changes to it.
      // The GetSignal function below will use the new system.
      
      // Fetch all necessary signals for this symbol's grids.
      // This is a simplification; ideally, only fetch what's needed by active grid strategies.
      // However, grid strategies already define their sources. GetSignal will be called by ProcessGridLogic.
      // The parameters to ProcessGridLogic (signalMA, etc.) were for passing PRE-CALCULATED signals.
      // Now, ProcessGridLogic will call GetSignal itself. So these parameters are not needed.
      // You need to modify ProcessGridLogic to call GetSignal for its trigger, strat1, strat2 sources.

      // For now, to keep ProcessGridLogic unchanged, we pass dummy values or values fetched via the new GetSignal.
      // This is not ideal but facilitates phased integration.
      // Let's assume ProcessGridLogic is NOT changed yet and still expects these.
      // We will fetch them using our new GetSignal.
      
      // This is inefficient as GetSignal might be called multiple times for the same source if multiple grids use it.
      // Caching within providers (once per bar) mitigates this.
      // The `ProcessGridLogic` function itself calls `GetSignal` to get `triggerSignal`, `strat1Signal`, `strat2Signal`.
      // So, the `signalMA, signalRSI...` parameters to `ProcessGridLogic` in its current form in your code
      // are actually NOT USED by the version of `ProcessGridLogic` you showed earlier.
      // That `ProcessGridLogic` fetches its own signals using `gridInstance.GetTriggerSource()` etc.
      // and then calls `GetSignal(symbolIdx, triggerSrc)`.
      // Therefore, we can pass dummy values here if that's the case, or remove them if PGL is updated.
      // For safety, assuming your PGL might still use them, let's fetch them once.

      Direction sigMA_forPGL   = GetSignal(SymbolLoop, SIGNAL_SRC_MA);
      Direction sigRSI_forPGL  = GetSignal(SymbolLoop, SIGNAL_SRC_RSI);
      Direction sigCSI_forPGL  = GetSignal(SymbolLoop, SIGNAL_SRC_CSI);
      Direction sigCFFP_forPGL = GetSignal(SymbolLoop, SIGNAL_SRC_CFFP);
      Direction sigCMSM_forPGL = GetSignal(SymbolLoop, SIGNAL_SRC_CMSM);

      if(CheckPointer(targetGrid1) != POINTER_INVALID)
        { ProcessGridLogic(targetGrid1, sigMA_forPGL, sigRSI_forPGL, sigCSI_forPGL, sigCFFP_forPGL, sigCMSM_forPGL); }
      if(CheckPointer(targetGrid2) != POINTER_INVALID)
        { ProcessGridLogic(targetGrid2, sigMA_forPGL, sigRSI_forPGL, sigCSI_forPGL, sigCFFP_forPGL, sigCMSM_forPGL); }
      if(CheckPointer(targetGrid3) != POINTER_INVALID)
        { ProcessGridLogic(targetGrid3, sigMA_forPGL, sigRSI_forPGL, sigCSI_forPGL, sigCFFP_forPGL, sigCMSM_forPGL); }

     } // --- End of Multi-Symbol Loop ---

   // --- Logic outside the symbol loop (runs once per tick) ---
   g_equityMonitor.OnTick();
   g_gridEngine.AllowGridStart(!g_equityMonitor.LimitReached());
   g_gridEngine.OnTick();
  }

//+------------------------------------------------------------------+
//| Chart Event handler                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
   const long& lparam,
   const double& dparam,
   const string& sparam)
{
// Pass chart events to modules that might use them (like the Display)
if(CheckPointer(GetPointer(g_display)) != POINTER_INVALID) g_display.ChartEvent(id, lparam, dparam, sparam);
if(CheckPointer(GetPointer(g_gridEngine)) != POINTER_INVALID) g_gridEngine.OnChartEvent(id, lparam, dparam, sparam);
if(CheckPointer(GetPointer(g_equityMonitor)) != POINTER_INVALID) g_equityMonitor.OnChartEvent(id, lparam, dparam, sparam);
}


// --- REMOVE Old Indicator Initialization & Signal Calculation Functions ---
// REMOVE: int InitRemainingIndicators() { ... }
// REMOVE: bool InitRSI(int symbolIndex, string symbol) { ... }
// REMOVE: bool MapCurrencyIndices(int symbolIndex, string symbol) { ... }
// REMOVE: bool MapCurrencyIndicesCFFP(int symbolIndex, string symbol) { ... }
// REMOVE: Direction GetSignalRSI(int symbolIndex, string symbol) { ... }
// REMOVE: Direction GetSignalCSI(int symbolIndex, string symbol) { ... }
// REMOVE: Direction GetSignalCFFP(int symbolIndex, string symbol) { ... }
// REMOVE: Direction GetSignalCMSM(int symbolIndex, string symbol) { ... }


//+------------------------------------------------------------------+
//| Get Signal State for a Specific Source (Main Routing Function)   |
//+------------------------------------------------------------------+
Direction GetSignal(const int symbolIndex, ENUM_SIGNAL_SOURCE source)
  {
   // Basic validation
   if(symbolIndex < 0 || symbolIndex >= NumberOfTradeableSymbols) 
     { PrintFormat("ORTBO GetSignal Error: Invalid symbolIndex %d.", symbolIndex); return None; }
   string symbol = SymbolArray[symbolIndex];
   if(symbol == "" || symbol == NULL) 
     { PrintFormat("ORTBO GetSignal Error: Invalid symbol string for index %d.", symbolIndex); return None; }

   // Check if the Signal Manager is initialized
   if(CheckPointer(g_theGlobalSignalManager) == POINTER_INVALID)
     {
      Print("ORTBO GetSignal Error: g_theGlobalSignalManager is not initialized!");
      return None;
     }

   // Use the new signal system for all sources that are configured to use it.
   // ORTBO_UseNewSignalSystemForSource will be updated as providers become stable.
   // For full Phase 2, all basic signals and their direct AND combinations should use the new system.
   // The CSignalManager's GetSignalResult handles both basic and composite sources.
   
   SignalResult result = g_theGlobalSignalManager.GetSignalResult(symbol, source);

   if(!result.isValid)
     {
      // Log error only if there's an error message, to avoid spamming for valid 'None' signals
      if(result.errorMessage != "")
         PrintFormat("ORTBO GetSignal (%s for %s): %s", EnumToString(source), symbol, result.errorMessage);
      return None; // Return None on error or if signal is legitimately None but invalid
     }
   
   return result.direction;
  }

//+------------------------------------------------------------------+
//| Process Grid Logic (Ensure this function calls the GetSignal above)|
//+------------------------------------------------------------------+
// Your existing ProcessGridLogic function. It should internally call:
// Direction triggerSignal = GetSignal(symbolIdx, triggerSrc);
// Direction strat1Signal  = GetSignal(symbolIdx, strat1Src);
// Direction strat2Signal  = GetSignal(symbolIdx, strat2Src);
// The parameters (sigMA, sigRSI, etc.) passed to it from OnTick are now effectively ignored
// if ProcessGridLogic fetches its own signals as shown in your original code.
// If your ProcessGridLogic *does not* call GetSignal itself and relies on passed params,
// then the OnTick fetching of sigMA_forPGL etc. is necessary.
// void ProcessGridLogic(Grid* gridInstance, Direction sigMA_dummy, Direction sigRSI_dummy, ... )
// {
//    ...
//    int symbolIdx = SymbolIndexBySymbol(gridInstance.symbol.Name());
//    Direction triggerSignal = GetSignal(symbolIdx, gridInstance.GetTriggerSource());
//    ...
// }


//+------------------------------------------------------------------+
//| Process Grid Logic (Corrected, Minimal Logging)                  |
//+------------------------------------------------------------------+
void ProcessGridLogic(GridInstance* gridInstance, Direction sigMA, Direction sigRSI, Direction sigCSI, Direction sigCFFP, Direction sigCMSM) // Takes signals as args
{
   // Ensure grid pointer is valid
   if(CheckPointer(gridInstance) == POINTER_INVALID)
   {
      Print("[ERROR] ORTBO: ProcessGridLogic called with invalid gridInstance pointer.");
      return;
   }

   // Cast to Grid* to access all properties
   Grid* grid = (Grid*)gridInstance;
   if(CheckPointer(grid) == POINTER_INVALID)
   {
      Print("[ERROR] ORTBO: Failed to cast GridInstance to Grid.");
      return;
   }

   // --- Get grid configuration and current state ---
   ulong gridMagic         = grid.magic;         // Direct access
   string gridSymbol       = grid.symbol.Name();
   string gridName         = grid.name;
   int currentGridCount    = gridInstance.GetCount();
   Direction currentGridDir= gridInstance.GetDirection();
   ENUM_SIGNAL_SOURCE triggerSrc = gridInstance.GetTriggerSource();
   ENUM_SIGNAL_SOURCE strat1Src  = gridInstance.GetStrat1Source();
   ENUM_SIGNAL_SOURCE strat2Src  = gridInstance.GetStrat2Source();
   ENUM_GRID_DIRECTION allowedDir = gridInstance.GetAllowedDirection();

   // --- Get actual signal values based on configuration ---
   int symbolIdx = SymbolIndexBySymbol(gridSymbol);
   if(symbolIdx < 0)
   {
        // Keep this error log as it prevents any action
        PrintFormat("[ERROR] ORTBO PGL[%s %lu %s]: Cannot find symbol index for %s. Aborting logic.",
                    gridName, gridMagic, gridSymbol, gridSymbol);
        return;
   }
   Direction triggerSignal = GetSignal(symbolIdx, triggerSrc);
   Direction strat1Signal  = GetSignal(symbolIdx, strat1Src);
   Direction strat2Signal  = GetSignal(symbolIdx, strat2Src);

   // --- Grid Starting Logic ---
   if (currentGridCount == 0)
   {
      // Check for existing positions
      int existingPositions = 0;
      CPositionInfo positionInfo;
      for(int k = PositionsTotal() - 1; k >= 0; k--)
      {
         if(positionInfo.SelectByIndex(k))
         {
            if (positionInfo.Magic() == grid.magic &&
                positionInfo.Symbol() == gridSymbol)
            {
               existingPositions++;
               // --- LOG: Informative log when preventing action ---
               PrintFormat("ORTBO PGL[%s %lu %s]: Found existing position #%lu. Grid start prevented.",
                           gridName, gridMagic, gridSymbol, positionInfo.Ticket());
               break; // Found one, no need to check further
            }
         }
         // Removed warning log for failed selection for minimal logging
      }

      // Only proceed if NO existing positions were found
      if(existingPositions == 0)
      {
         // Check trigger signal
         if(triggerSignal == Buy || triggerSignal == Sell)
         {
            // Check allowed direction
            bool directionAllowed = false;
            if (allowedDir == GRID_DIR_BOTH) { directionAllowed = true; }
            else if (allowedDir == GRID_DIR_BUY_ONLY && triggerSignal == Buy) { directionAllowed = true; }
            else if (allowedDir == GRID_DIR_SELL_ONLY && triggerSignal == Sell) { directionAllowed = true; }

            // If conditions met, attempt to start
            if(directionAllowed)
            {
               // --- LOG: Action about to be taken ---
               PrintFormat("ORTBO PGL[%s %lu %s]: Starting grid. Trigger: %s (%s), Direction: %s",
                           gridName, gridMagic, gridSymbol,
                           EnumToString(triggerSrc), EnumToString(triggerSignal), EnumToString(triggerSignal));
               g_gridEngine.StartGrid(gridInstance, triggerSignal);
            }
            // Removed log for 'direction not allowed'
         }
         // Removed log for 'trigger signal is None'
      }
      // Removed log for 'existing positions > 0'
   }
   // --- Grid Extending Logic ---
   else // currentGridCount > 0
   {
      // Check grid direction validity
      if(currentGridDir == None )
      {
         // Keep this warning as it indicates a potential state issue preventing action
         PrintFormat("[WARN] ORTBO PGL[%s %lu %s]: Grid count is %d but direction is %s. Skipping extension check.",
                      gridName, gridMagic, gridSymbol, currentGridCount, EnumToString(currentGridDir));
         return;
      }

      // 1. Check distance
      bool distanceMet = gridInstance.CheckDistance();

      if(distanceMet) // Only proceed if distance is met
      {
         // 2. Check allowed direction
         bool directionAllowed = false;
         if (allowedDir == GRID_DIR_BOTH) { directionAllowed = true; }
         else if (allowedDir == GRID_DIR_BUY_ONLY && currentGridDir == Buy) { directionAllowed = true; }
         else if (allowedDir == GRID_DIR_SELL_ONLY && currentGridDir == Sell) { directionAllowed = true; }

         // 3. Check strategy signals
         bool signalAllowsExtension = false;
         string allowingSignal = ""; // Initialize empty
         if (strat1Signal == currentGridDir) { signalAllowsExtension = true; allowingSignal = EnumToString(strat1Src); }
         if (strat2Signal == currentGridDir)
         {
              if (signalAllowsExtension) allowingSignal += "+" + EnumToString(strat2Src);
              else { signalAllowsExtension = true; allowingSignal = EnumToString(strat2Src); }
         }

         // If all conditions met, attempt to extend
         // The CheckDistance call above set grid.nextGridLevelHit if true
         if (grid.nextGridLevelHit && directionAllowed && signalAllowsExtension)
         {
              // --- LOG: Action about to be taken ---
              PrintFormat("ORTBO PGL[%s %lu %s]: Extending grid (Level %d). Direction: %s, Signal: %s",
                          gridName, gridMagic, gridSymbol, currentGridCount + 1,
                          EnumToString(currentGridDir), allowingSignal);
              g_gridEngine.ExtendGrid(gridInstance);
         }
         // Removed log for 'conditions not met for extension'
      }
      // Removed log for 'distance not met'
   } // End of Grid Extending Logic
}

//+------------------------------------------------------------------+
//| SymbolIndexBySymbol                                              |
//+------------------------------------------------------------------+
int SymbolIndexBySymbol(const string symbol)
  {
   for(int i = 0; i < NumberOfTradeableSymbols; i++)
     {
      if(SymbolArray[i] == symbol)
        return i;
     }
   return -1;
  }

