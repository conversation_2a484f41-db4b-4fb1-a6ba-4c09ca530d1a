//+------------------------------------------------------------------+
//|                                                  CCMSMConfig.mqh |
//|                                      Copyright 2025, Your Name   |
//|                                     https://www.yourwebsite.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name"
#property link      "https://www.yourwebsite.com"
#property version   "1.00"

#ifndef ORTBO_CCMSM_CONFIG_MQH
#define ORTBO_CCMSM_CONFIG_MQH

#include "CProviderConfigBase.mqh"

//+------------------------------------------------------------------+
//| CMSM provider configuration                                      |
//+------------------------------------------------------------------+
class CCMSMConfig : public CProviderConfigBase
  {
private:
   double            m_tradeLevel;        // Trade level threshold
   int               m_timeIndex;         // Time index parameter
   // Note: Additional CMSM parameters can be added here as needed
   
public:
   //--- Constructor with default values
                     CCMSMConfig() : m_tradeLevel(2.0),
                                     m_timeIndex(0)
     {
      m_configType = "CMSM";
     }
   
   //--- Constructor with parameters
                     CCMSMConfig(double tradeLevel,
                                 ENUM_TIMEFRAMES indicatorTimeframe,
                                 int timeIndex,
                                 bool filterInverter)
     {
      m_configType = "CMSM";
      m_tradeLevel = tradeLevel;
      m_timeframe = indicatorTimeframe;  // For CMSM, timeframe is the indicator timeframe
      m_timeIndex = timeIndex;
      m_filterInverter = filterInverter;
      m_enabled = true;
     }
   
   //--- Validation
   virtual bool      Validate() override
     {
      string error;
      
      // Validate common parameters
      if(!ValidateCommon(error))
        {
         SetValidationError(error);
         return false;
        }
      
      // Validate trade level
      if(!CConfigValidator::ValidateDoubleRange(m_tradeLevel, 0.0, 10.0, error))
        {
         SetValidationError("CMSM Trade Level: " + error);
         return false;
        }
      
      // Validate time index
      if(!CConfigValidator::ValidateIntRange(m_timeIndex, 0, 100, error))
        {
         SetValidationError("CMSM Time Index: " + error);
         return false;
        }
      
      ClearValidationError();
      return true;
     }
   
   //--- Serialization
   virtual bool      LoadFromString(const string configString) override
     {
      string params[];
      int count = StringSplit(configString, '|', params);
      
      if(count < 5)
        {
         SetValidationError("Invalid configuration string format");
         return false;
        }
      
      int index = 0;
      
      // Parse common parameters
      if(!ParseCommonParams(params, index))
        {
         SetValidationError("Failed to parse common parameters");
         return false;
        }
      
      // Parse CMSM-specific parameters
      m_tradeLevel = StringToDouble(params[index++]);
      m_timeIndex = (int)StringToInteger(params[index++]);
      
      return Validate();
     }
   
   virtual string    SaveToString() const override
     {
      string common = CommonParamsToString();
      string specific = StringFormat("%.2f|%d", m_tradeLevel, m_timeIndex);
      return common + "|" + specific;
     }
   
   //--- Clone
   virtual CConfigBase* Clone() const override
     {
      CCMSMConfig* clone = new CCMSMConfig();
      clone.m_enabled = m_enabled;
      clone.m_timeframe = m_timeframe;
      clone.m_filterInverter = m_filterInverter;
      clone.m_tradeLevel = m_tradeLevel;
      clone.m_timeIndex = m_timeIndex;
      clone.m_configName = m_configName;
      clone.m_version = m_version;
      return clone;
     }
   
   //--- Getters
   double            GetTradeLevel() const { return m_tradeLevel; }
   int               GetTimeIndex() const { return m_timeIndex; }
   
   //--- Setters
   void              SetTradeLevel(double level) 
     { 
      m_tradeLevel = level; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetTimeIndex(int index) 
     { 
      m_timeIndex = index; 
      m_lastModified = TimeCurrent();
     }
  };

#endif // ORTBO_CCMSM_CONFIG_MQH