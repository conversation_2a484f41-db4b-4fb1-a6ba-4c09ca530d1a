//+------------------------------------------------------------------+
//|                                                        ORTBO.mq5 |
//|                                           Copyright 2025, philuk |
//|                                        mailto:<EMAIL> |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, philuk"
#property link      "mailto:<EMAIL>"
#property version   "1.11" // Replaced ArraySort with Manual Sort in GetSignalCFFP
#property strict

// --- Resource Embedding ---
// Embed the compiled CSI indicator directly into the EA executable
#resource "CURRENCY_STRENGTH_INDEX.ex5"
// Embed the compiled CCFp indicator directly into the EA executable
#resource "CCFp.ex5"
// Embed the compiled CMSM17 indicator directly into the EA executable
#resource "\\Indicators\\###CMSMIndV17.06 (1).ex5"
// Embed the compiled CMSM24 indicator directly into the EA executable
#resource "\\Indicators\\###CMSMIndV24.01 (1).ex5"

#define EA_VERSION "1.11c" // Updated version


// --- Includes ---
#include <ORTBO_Enums.mqh> // Include the separate file with shared enums (Ensure SIGNAL_SRC_CFFP is added here)
#include <EquityMonitor.mqh>
#include <GridEngine.mqh> // Includes corrected GridEngine v1.06


// --- Define Number of Grid Strategies Per Symbol ---
// This constant determines how many independent grid strategies (e.g., MACH1, MACH2, MACH3)
// are run for each symbol in the list. The magic numbers and signal logic
// are assigned based on this.
#define NUMBER_OF_GRIDS_PER_SYMBOL 3 // MODIFIED: Set to 3 for MACH1, MACH2, and MACH3

// --- Inputs ---
input group "Main"
input MULTISYMBOL InputMultiSymbol = Current; // Symbols To Trade
input string      AllTradableSymbols = "EURUSD|GBPUSD|USDJPY|AUDUSD|USDCAD|NZDUSD|USDCHF"; // Symbols to trade if 'All' is selected
// --- Base Magic Numbers for each Grid Strategy ---
input ulong       InpMagicMach1 = 1100; // Base Magic# for MACH1 grids (+1 per symbol)
input ulong       InpMagicMach2 = 2200; // Base Magic# for MACH2 grids (+1 per symbol)
input ulong       InpMagicMach3 = 3300; // ADDED: Base Magic# for MACH3 grids (+1 per symbol)


// --- Grid Strategy Signal Configuration ---
input group "MACH1 Signal Configuration"
input ENUM_SIGNAL_SOURCE InpMach1TriggerSource = SIGNAL_SRC_CSI; // Trigger Signal for MACH1 Grid
input ENUM_SIGNAL_SOURCE InpMach1Strat1Source  = SIGNAL_SRC_MA;  // Strat1 Signal for MACH1 Grid
input ENUM_SIGNAL_SOURCE InpMach1Strat2Source  = SIGNAL_SRC_RSI; // Strat2 Signal for MACH1 Grid
input ENUM_GRID_DIRECTION InpMach1Direction    = GRID_DIR_BUY_ONLY; // Allowed Direction(s) for MACH1 Grid

input group "MACH2 Signal Configuration"
input ENUM_SIGNAL_SOURCE InpMach2TriggerSource = SIGNAL_SRC_MA;  // Trigger Signal for MACH2 Grid
input ENUM_SIGNAL_SOURCE InpMach2Strat1Source  = SIGNAL_SRC_RSI; // Strat1 Signal for MACH2 Grid
input ENUM_SIGNAL_SOURCE InpMach2Strat2Source  = SIGNAL_SRC_CSI; // Strat2 Signal for MACH2 Grid
input ENUM_GRID_DIRECTION InpMach2Direction    = GRID_DIR_BOTH; // Allowed Direction(s) for MACH2 Grid

input group "MACH3 Signal Configuration"
input ENUM_SIGNAL_SOURCE InpMach3TriggerSource = SIGNAL_SRC_RSI; // Trigger Signal for MACH3 Grid
input ENUM_SIGNAL_SOURCE InpMach3Strat1Source  = SIGNAL_SRC_MA;  // Strat1 Signal for MACH3 Grid
input ENUM_SIGNAL_SOURCE InpMach3Strat2Source  = SIGNAL_SRC_NONE;// Strat2 Signal for MACH3 Grid
input ENUM_GRID_DIRECTION InpMach3Direction    = GRID_DIR_BOTH; // Allowed Direction(s) for MACH3 Grid


input group "Moving Average (MA) Signal"
input bool               InpEnableSignalMovingAverage = true; // Enable Moving Average Signal
input bool               InpMaFilterInverter   = false; // If True, Invert Filter
input ENUM_TIMEFRAMES    InpMaFrame            = PERIOD_M1; // Moving Average TimeFrame
input int                InpMaPeriod           = 3; // Moving Average Period
input ENUM_MA_METHOD     InpMaMethod           = MODE_EMA; // Moving Average Method
input ENUM_APPLIED_PRICE InpMaPrice            = PRICE_CLOSE; // Moving Average Price
input int                InpMaShift            = 0; // Moving Average Shift
input int                InpMaMargin           = 6; // Moving Average Margin To Signal (Points)

input group "Relative Strength Index (RSI) Signal"
input bool               InpEnableSignalRSI    = true; // Enable RSI Signal
input bool               InpRSIFilterInverter  = false; // If True, Invert Filter
input ENUM_TIMEFRAMES    InpRSIFrame           = PERIOD_H1; // RSI TimeFrame
input int                InpRsiPeriod          = 14; // RSI Period
input double             InpRsiMinimum         = 30.0; // RSI Minimum (Buy Threshold)
input double             InpRsiMaximum         = 70.0; // RSI Maximum (Sell Threshold)

// --- ADDED: CSI Inputs ---
input group "Currency Strength Index (CSI) Signal"
input bool               InpEnableSignalCSI    = true; // Enable CSI Signal
input ENUM_TIMEFRAMES    InpCSIFrame           = PERIOD_CURRENT;// CSI Timeframe (IMPORTANT for update frequency)
input int                InpCSIMAPeriod        = 20; // CSI MA Period (Default: 20)
input int                InpCSIMADelta         = 1; // CSI MA Delta (Default: 1)
input bool               InpCSIFilterInverter  = false; // If True, Invert Filter (Less intuitive for CSI)

// --- ADDED: CFFP Inputs ---
input group "Currency Strength (CFFP) Signal"
input bool               InpEnableSignalCFFP    = true;          // Enable CFFP Signal
input ENUM_TIMEFRAMES    InpCFFPFrame           = PERIOD_CURRENT;// CFFP Timeframe (IMPORTANT for resource iCustom)
input int                InpCFFPFastMAPeriod    = 3;             // CFFP Fast MA Period [from CCFp.ex5]
input int                InpCFFPSlowMAPeriod    = 5;             // CFFP Slow MA Period [from CCFp.ex5]
input ENUM_MA_METHOD     InpCFFPMAMethod        = MODE_SMA;      // CFFP MA Method [from CCFp.ex5]
input ENUM_APPLIED_PRICE InpCFFPAppliedPrice    = PRICE_CLOSE;   // CFFP Applied Price [from CCFp.ex5]
input bool               InpCFFPFilterInverter  = false;         // If True, Invert CFFP Signal

// --- ADDED: CMSM17 Inputs ---
input group "Currency Momentom Strength Meter (CMSM17) Signal";
input bool   InpEnableSignalCMSM    = true;  // Enable CMSM17 Signal (Reads Chart Label)
input bool   InpCMSMFilterInverter  = false; // If True, Invert CMSM17 Signal Logic

// --- ADDED: Inputs corresponding to CMSM.ex5 parameters ---
// Add ALL necessary inputs for CMSM here, matching their types and default values
// Example (based on previous screenshots - VERIFY TYPES AND DEFAULTS):
input double          InpCMSM_TradeLevel   = 2.0;
input ENUM_TIMEFRAMES InpCMSMTimeFrame    = PERIOD_W1; 
input int             InpCMSM_TimeIndex   = 0;  
/*  
input int             InpCMSM17_Fast_MA_Period   = 3;     
input int             InpCMSM17_Slow_MA_Period   = 5;           
input ENUM_MA_METHOD  InpCMSM17_MA_Method   = MODE_EMA;       
input ENUM_APPLIED_PRICE InpCMSM17_Price_Type = PRICE_WEIGHTED;   
input string          InpCMSM17_PeriodMultiplier  = "1,2,3,4,8,12";
input double          InpCMSM17_USD_Weight   = 1.0;
input double          InpCMSM17_EUR_Weight   = 1.0;
input double          InpCMSM17_GBP_Weight   = 0.98;
input double          InpCMSM17_CHF_Weight   = 0.96;
input double          InpCMSM17_JPY_Weight   = 0.94;
input double          InpCMSM17_CAD_Weight   = 0.92;
input double          InpCMSM17_AUD_Weight   = 0.90;
input double          InpCMSM17_NZD_Weight   = 0.90;
input string          InpCMSM17_SymbolPrefix  = "";
input string          InpCMSM17_SymbolSuffix  = "";
input int             InpCMSM17_IndicatorID   = 1;    


input int             InpCMSM_Top_Rank    = 2;             
input int             InpCMSM_Bottom_Rank = 2;  
*/           
// ... add ALL OTHER CMSM inputs that need configuration ...
// Note: Cosmetic inputs like Corner, Colors, Show_Panel might not need to be mirrored unless desired.


// --- Include EquityMonitor Inputs ---
input group "" // Separator
// EquityMonitor inputs are defined within EquityMonitor.mqh

// --- Include GridEngine Inputs ---
input group "" // Separator
// GridEngine inputs are defined within GridEngine.mqh
// Note: GridEngine now includes inputs for Initial Lot Mode, Risk%, SL Points, Grid Width Mode.

// --- Global Objects ---
EquityMonitor g_equityMonitor;
GridEngine    g_gridEngine;
GridInstance* g_gridInstances[];
Display       g_display;

// --- Multi-Symbol Globals ---
string SymbolArray[];
int    NumberOfTradeableSymbols = 0;

// --- Indicator Handle Arrays ---
int   m_MAHandle[];
int   m_RSIHandle[];

// --- Single Instance Indicator Handles (for CSI, CFFP, CMSM) ---
int   m_csiHandle  = INVALID_HANDLE; // Global handle for single CSI instance
int   m_cffpHandle = INVALID_HANDLE; // Global handle for single CFFP instance
int   m_cmsmHandle = INVALID_HANDLE; // Global handle for single CMSM instance

// --- Indicator Buffer Arrays (resized in OnInit) ---
double m_MABuffer[];
double m_RSIBuffer[];

// --- CSI Currency Mapping ---
string m_CurrencyNames[8] = {"EUR", "GBP", "AUD", "NZD", "USD", "CAD", "CHF", "JPY"}; // Order for original CSI
int    m_BaseCurrencyIndex[];
int    m_QuoteCurrencyIndex[];

// --- CSI Optimization Variables ---
datetime m_CSILastBarTime[]; // Stores timestamp of the last bar on InpCSIFrame where signal was updated
Direction m_CSILastSignal[]; // Stores the last calculated CSI signal

// --- ADDED: CFFP Currency Mapping & Optimization ---
string m_CFFPCurrencyNames[8] = {"USD", "EUR", "GBP", "CHF", "JPY", "AUD", "CAD", "NZD"}; // Order from CCFp.ex5 buffers 0-7
int    m_CFFPBaseCurrencyIndex[];
int    m_CFFPQuoteCurrencyIndex[];
datetime m_CFFPLastBarTime[]; // Stores timestamp of the last bar on InpCFFPFrame where signal was updated
Direction m_CFFPLastSignal[]; // Stores the last calculated CFFP signal

// +++ NEW: MA, RSI, CMSM Optimization Variables +++
// --- MA Optimization Variables ---
datetime m_MALastBarTime[];   // Stores timestamp of the last bar on InpMaFrame where signal was updated
Direction m_MALastSignal[];   // Stores the last calculated MA signal

// --- RSI Optimization Variables ---
datetime m_RSILastBarTime[];  // Stores timestamp of the last bar on InpRSIFrame where signal was updated
Direction m_RSILastSignal[];  // Stores the last calculated RSI signal

// --- CMSM Optimization Variables ---
datetime m_CMSMLastBarTime[]; // Stores timestamp of the last bar on InpCMSM17TimeFrame where signal was updated
Direction m_CMSMLastSignal[]; // Stores the last calculated CMSM signal

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
  // --- VERY FIRST LOG ---
  PrintFormat("ORTBO v%s – OnInit Start.", EA_VERSION);

  PrintFormat("ORTBO v%s – Step 2: Setting up Multi-Symbol Array...", EA_VERSION);
  // --- Setup Multi-Symbol Array ---
  if(InputMultiSymbol == Current)
  {
     NumberOfTradeableSymbols = 1;
     ArrayResize(SymbolArray, NumberOfTradeableSymbols);
     SymbolArray[0] = _Symbol;
     PrintFormat("ORTBO: Multi-Symbol Mode: Current. Trading %s", SymbolArray[0]);
  }
  else // All
  {
     NumberOfTradeableSymbols = StringSplit(AllTradableSymbols, '|', SymbolArray);
     if(NumberOfTradeableSymbols <= 0)
     {
        PrintFormat("[ERROR] ORTBO: No valid symbols found in AllTradableSymbols input string: '%s'. Check the format (use '|' as separator).", AllTradableSymbols);
        return INIT_FAILED;
     }
     ArrayResize(SymbolArray, NumberOfTradeableSymbols);
     PrintFormat("ORTBO: Multi-Symbol Mode: All. Processing %d Symbols: %s", NumberOfTradeableSymbols, AllTradableSymbols);
     for(int i=0; i<NumberOfTradeableSymbols; i++)
     {
        if(!SymbolSelect(SymbolArray[i], true))
        {
           PrintFormat("[ERROR] ORTBO: Failed to select symbol '%s' in Market Watch. Error: %d. Ensure it's available and enabled.", SymbolArray[i], GetLastError());
           // return INIT_FAILED; // Comment out to allow partial trading if some symbols fail
        }
        MqlTick tick;
        if(!SymbolInfoTick(SymbolArray[i], tick))
        {
           PrintFormat("[WARN] ORTBO: Could not get initial tick data for symbol '%s'. Error: %d. May affect initial calculations.", SymbolArray[i], GetLastError());
        }
     }
  }
  PrintFormat("ORTBO v%s – Step 3: Multi-Symbol Setup Complete.", EA_VERSION);

  PrintFormat("ORTBO v%s – Step 4: Checking Magic Numbers...", EA_VERSION);
  // --- Magic Number Overlap Check ---
  ulong maxMagic1 = InpMagicMach1 + NumberOfTradeableSymbols;
  ulong maxMagic2 = InpMagicMach2 + NumberOfTradeableSymbols;
  ulong maxMagic3 = InpMagicMach3 + NumberOfTradeableSymbols; // ADDED check for MACH3

  // Check MACH1 vs MACH2
  if ((InpMagicMach1 < maxMagic2 && maxMagic1 > InpMagicMach2) ||
      (InpMagicMach2 < maxMagic1 && maxMagic2 > InpMagicMach1))
  {
      PrintFormat("[ERROR] ORTBO: Magic number ranges may overlap! MACH1 (%d-%d) and MACH2 (%d-%d)",
                  InpMagicMach1 + 1, maxMagic1, InpMagicMach2 + 1, maxMagic2);
      Print("Ensure InpMagicMach1, InpMagicMach2, and InpMagicMach3 are sufficiently separated.");
      return INIT_FAILED;
  }
  // Check MACH1 vs MACH3
  if ((InpMagicMach1 < maxMagic3 && maxMagic1 > InpMagicMach3) ||
      (InpMagicMach3 < maxMagic1 && maxMagic3 > InpMagicMach1))
  {
      PrintFormat("[ERROR] ORTBO: Magic number ranges may overlap! MACH1 (%d-%d) and MACH3 (%d-%d)",
                  InpMagicMach1 + 1, maxMagic1, InpMagicMach3 + 1, maxMagic3);
      Print("Ensure InpMagicMach1, InpMagicMach2, and InpMagicMach3 are sufficiently separated.");
      return INIT_FAILED;
  }
  // Check MACH2 vs MACH3
  if ((InpMagicMach2 < maxMagic3 && maxMagic2 > InpMagicMach3) ||
      (InpMagicMach3 < maxMagic2 && maxMagic3 > InpMagicMach2))
  {
      PrintFormat("[ERROR] ORTBO: Magic number ranges may overlap! MACH2 (%d-%d) and MACH3 (%d-%d)",
                  InpMagicMach2 + 1, maxMagic2, InpMagicMach3 + 1, maxMagic3);
      Print("Ensure InpMagicMach1, InpMagicMach2, and InpMagicMach3 are sufficiently separated.");
      return INIT_FAILED;
  }

  PrintFormat("ORTBO: Magic number ranges: MACH1 [%d - %d], MACH2 [%d - %d], MACH3 [%d - %d]",
              InpMagicMach1 + 1, maxMagic1, InpMagicMach2 + 1, maxMagic2, InpMagicMach3 + 1, maxMagic3);
  PrintFormat("ORTBO v%s – Step 5: Magic Number Check Complete.", EA_VERSION);

  // --- CORRECTED ORDER: Initialize Modules BEFORE Display Creation ---

  PrintFormat("ORTBO v%s – Step 10: Initializing EquityMonitor...", EA_VERSION);
  // --- Initialise EquityMonitor ---
  int init = g_equityMonitor.OnInit(GetPointer(g_display));
  if (init != INIT_SUCCEEDED)
  {
     PrintFormat("[ERROR] ORTBO: EquityMonitor initialization failed. Code: %d", init);
     return init;
  }
  Print("ORTBO: EquityMonitor Initialized.");
  PrintFormat("ORTBO v%s – Step 11: EquityMonitor Initialized OK.", EA_VERSION);

  PrintFormat("ORTBO v%s – Step 12: Creating Grid Instances...", EA_VERSION);
  // --- Dynamically create grids ---
  int totalGrids = NumberOfTradeableSymbols * NUMBER_OF_GRIDS_PER_SYMBOL; // MODIFIED: Uses new define
  ArrayResize(g_gridInstances, totalGrids);
  PrintFormat("ORTBO: Resized grid instance array for %d total grids (%d symbols x %d grids/symbol)", totalGrids, NumberOfTradeableSymbols, NUMBER_OF_GRIDS_PER_SYMBOL);
  for(int i = 0; i < NumberOfTradeableSymbols; i++)
  {
     string currentSymbol = SymbolArray[i];

     // --- Create MACH1 Grid ---
     ulong magic1 = InpMagicMach1 + (i + 1);
     string name1 = "MACH1_" + currentSymbol;
     int index1 = i * NUMBER_OF_GRIDS_PER_SYMBOL + 0; // MODIFIED: Index calculation
     if(index1 >= totalGrids)
     {
        PrintFormat("[ERROR] ORTBO: Calculated index for grid %s is out of bounds (%d >= %d).", name1, index1, totalGrids);
        return INIT_FAILED;
     }
     g_gridInstances[index1] = g_gridEngine.CreateGrid(name1, magic1, currentSymbol);
     if(g_gridInstances[index1] == NULL) // Use NULL check
     {
        PrintFormat("[ERROR] ORTBO: Failed to create grid instance %s for %s (Magic: %d) - CreateGrid returned NULL.", name1, currentSymbol, magic1);
        return INIT_FAILED;
     }
     // Set specific parameters for MACH1
     g_gridInstances[index1].SetRiskParameters(InpInitialLotMode, InpRiskPercent, InpInitialSLPoints);
     g_gridInstances[index1].SetGridWidthParameters(InpGridWidthMode);
     // MODIFIED: Set Signal Configuration using new inputs
     g_gridInstances[index1].SetSignalConfiguration(InpMach1TriggerSource, InpMach1Strat1Source, InpMach1Strat2Source, InpMach1Direction);
     PrintFormat("ORTBO: Created MACH1 grid for %s (Magic: %d, Index: %d, Cfg: %s,%s,%s,%s)", currentSymbol, magic1, index1,
                 EnumToString(InpMach1TriggerSource), EnumToString(InpMach1Strat1Source), EnumToString(InpMach1Strat2Source), EnumToString(InpMach1Direction));

     // --- Create MACH2 Grid ---
     ulong magic2 = InpMagicMach2 + (i + 1);
     string name2 = "MACH2_" + currentSymbol;
     int index2 = i * NUMBER_OF_GRIDS_PER_SYMBOL + 1; // MODIFIED: Index calculation
     if(index2 >= totalGrids)
     {
        PrintFormat("[ERROR] ORTBO: Calculated index for grid %s is out of bounds (%d >= %d).", name2, index2, totalGrids);
        return INIT_FAILED;
     }
     g_gridInstances[index2] = g_gridEngine.CreateGrid(name2, magic2, currentSymbol);
     if(g_gridInstances[index2] == NULL) // Use NULL check
     {
        PrintFormat("[ERROR] ORTBO: Failed to create grid instance %s for %s (Magic: %d) - CreateGrid returned NULL.", name2, currentSymbol, magic2);
        return INIT_FAILED;
     }
     // Set specific parameters for MACH2
     g_gridInstances[index2].SetRiskParameters(InpInitialLotMode, InpRiskPercent, InpInitialSLPoints);
     g_gridInstances[index2].SetGridWidthParameters(InpGridWidthMode);
     // MODIFIED: Set Signal Configuration using new inputs
     g_gridInstances[index2].SetSignalConfiguration(InpMach2TriggerSource, InpMach2Strat1Source, InpMach2Strat2Source, InpMach2Direction);
     PrintFormat("ORTBO: Created MACH2 grid for %s (Magic: %d, Index: %d, Cfg: %s,%s,%s,%s)", currentSymbol, magic2, index2,
                 EnumToString(InpMach2TriggerSource), EnumToString(InpMach2Strat1Source), EnumToString(InpMach2Strat2Source), EnumToString(InpMach2Direction));

     // --- ADDED: Create MACH3 Grid ---
     ulong magic3 = InpMagicMach3 + (i + 1);
     string name3 = "MACH3_" + currentSymbol;
     int index3 = i * NUMBER_OF_GRIDS_PER_SYMBOL + 2; // MODIFIED: Index calculation
     if(index3 >= totalGrids)
     {
        PrintFormat("[ERROR] ORTBO: Calculated index for grid %s is out of bounds (%d >= %d).", name3, index3, totalGrids);
        return INIT_FAILED;
     }
     g_gridInstances[index3] = g_gridEngine.CreateGrid(name3, magic3, currentSymbol);
     if(g_gridInstances[index3] == NULL) // Use NULL check
     {
        PrintFormat("[ERROR] ORTBO: Failed to create grid instance %s for %s (Magic: %d) - CreateGrid returned NULL.", name3, currentSymbol, magic3);
        return INIT_FAILED;
     }
     // Set specific parameters for MACH3
     g_gridInstances[index3].SetRiskParameters(InpInitialLotMode, InpRiskPercent, InpInitialSLPoints);
     g_gridInstances[index3].SetGridWidthParameters(InpGridWidthMode);
     // MODIFIED: Set Signal Configuration using new inputs
     g_gridInstances[index3].SetSignalConfiguration(InpMach3TriggerSource, InpMach3Strat1Source, InpMach3Strat2Source, InpMach3Direction);
     PrintFormat("ORTBO: Created MACH3 grid for %s (Magic: %d, Index: %d, Cfg: %s,%s,%s,%s)", currentSymbol, magic3, index3,
                 EnumToString(InpMach3TriggerSource), EnumToString(InpMach3Strat1Source), EnumToString(InpMach3Strat2Source), EnumToString(InpMach3Direction));
  }
  PrintFormat("ORTBO v%s – Step 13: Grid Instances Created OK.", EA_VERSION);


  PrintFormat("ORTBO v%s – Step 14: Initializing GridEngine...", EA_VERSION);
  // --- Initialise GridEngine ---
  init = g_gridEngine.OnInit(GetPointer(g_display));
  if (init != INIT_SUCCEEDED)
  {
     PrintFormat("[ERROR] ORTBO: GridEngine initialization failed. Code: %d", init);
     return init;
  }
  Print("ORTBO: GridEngine Initialized.");
  PrintFormat("ORTBO v%s – Step 15: GridEngine Initialized OK.", EA_VERSION);

  // --- CORRECTED ORDER: Now Initialize Display ---

  PrintFormat("ORTBO v%s – Step 6: Calling CreateCommonDisplay()...", EA_VERSION);
  // --- Initialise Common Display ---
  init = CreateCommonDisplay();
  if (init != INIT_SUCCEEDED)
  {
      PrintFormat("ORTBO v%s – Step 6 FAILED: CreateCommonDisplay() returned error code %d.", EA_VERSION, init);
      return init;
  }
  PrintFormat("ORTBO v%s – Step 7: CreateCommonDisplay() Succeeded.", EA_VERSION);

  // --- Display Pointer Check ---
  PrintFormat("ORTBO v%s – Step 8: Checking Display Pointer...", EA_VERSION);
  if(CheckPointer(GetPointer(g_display)) == POINTER_INVALID)
  {
     PrintFormat("[ERROR] ORTBO: Display object pointer is invalid after CreateCommonDisplay.");
     return INIT_FAILED;
  }
  Print("ORTBO: Common Display Initialized.");
  PrintFormat("ORTBO v%s – Step 9: Display Pointer OK.", EA_VERSION);

  // --- CORRECTED ORDER: Initialize Indicators Last ---

  PrintFormat("ORTBO v%s – Step 16: Initializing Indicators...", EA_VERSION);
  // --- Initialise indicators ---
  init = InitIndicators(); // Includes MA, RSI, CSI, and CFFP initialization
  if (init != INIT_SUCCEEDED)
  {
     PrintFormat("[ERROR] ORTBO: Indicator initialization failed. Code: %d", init);
     return init;
  }
  Print("ORTBO: Indicators Initialized.");
  PrintFormat("ORTBO v%s – Step 17: Indicators Initialized OK.", EA_VERSION);

  PrintFormat("ORTBO v%s: Initialization complete.", EA_VERSION);
  return INIT_SUCCEEDED;
}


//+------------------------------------------------------------------+
//| Create the common display panel                                  |
//+------------------------------------------------------------------+
int CreateCommonDisplay()
{
  PrintFormat("ORTBO: Entering CreateCommonDisplay()...");

  PrintFormat("ORTBO: Calling g_equityMonitor.Rows()...");
  int eqRows = g_equityMonitor.Rows();
  PrintFormat("ORTBO: g_equityMonitor.Rows() returned %d.", eqRows);
  PrintFormat("ORTBO: Calling g_gridEngine.Rows()...");
  int geRows = g_gridEngine.Rows();
  PrintFormat("ORTBO: g_gridEngine.Rows() returned %d.", geRows);

  PrintFormat("ORTBO: Calculating total rows...");
  int rows = 1 + eqRows + 1 + 1 + geRows;
  PrintFormat("ORTBO: Total display rows calculated: %d", rows);

  PrintFormat("ORTBO: Calling g_display.Create()...");
  // Create the main dialog window
  if (!g_display.Create("ORTBO v" + EA_VERSION, 2, rows, 100))
  {
    Print("[ERROR] ORTBO: Could not initialize display dialog (g_display.Create failed).");
    return INIT_FAILED;
  }
  PrintFormat("ORTBO: g_display.Create() succeeded.");

  int row = 0;
  // --- Create Equity Monitor section ---
  PrintFormat("ORTBO: Creating EquityMonitor title row...");
  if (!g_display.CreateRow("EquityMonitor", g_equityMonitor.Name(), row, 8, clrGold))
  {
     Print("[ERROR] ORTBO: Failed to create EquityMonitor title row in display.");
     return INIT_FAILED;
  }
  row++;
  PrintFormat("ORTBO: Creating EquityMonitor content rows...");
  if (!g_equityMonitor.CreateRows(row))
  {
     Print("[ERROR] ORTBO: Failed to create EquityMonitor content rows.");
     return INIT_FAILED;
  }
  row += eqRows;
  row++; // Gap

  // --- Create Grid Engine section ---
  PrintFormat("ORTBO: Creating GridEngine title row...");
  if (!g_display.CreateRow("GridEngine", g_gridEngine.Name(), row, 8, clrGold))
  {
     Print("[ERROR] ORTBO: Failed to create GridEngine title row in display.");
     return INIT_FAILED;
  }
  row++;
  PrintFormat("ORTBO: Creating GridEngine content rows...");
  if (!g_gridEngine.CreateRows(row))
  {
     Print("[ERROR] ORTBO: Failed to create GridEngine content rows.");
     return INIT_FAILED;
  }
  // row += geRows; // GridEngine manages its own row count internally now

  PrintFormat("ORTBO: Calling g_display.Run()...");
  // Start the display
  if (!g_display.Run())
  {
    Print("[ERROR] ORTBO: Could not start (run) the display dialog (g_display.Run failed).");
    return INIT_FAILED;
  }
  PrintFormat("ORTBO: g_display.Run() succeeded. Exiting CreateCommonDisplay() successfully.");

  return INIT_SUCCEEDED;
}


//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
  PrintFormat("ORTBO: Deinitializing... Reason: %d", reason);

  // Release Per-Symbol Indicator Handles (MA and RSI)
  for(int i = 0; i < NumberOfTradeableSymbols; i++)
  {
    if(ArraySize(m_MAHandle) > i && m_MAHandle[i] != INVALID_HANDLE)
    {
      IndicatorRelease(m_MAHandle[i]);
    }
    if(ArraySize(m_RSIHandle) > i && m_RSIHandle[i] != INVALID_HANDLE)
    {
      IndicatorRelease(m_RSIHandle[i]);
    }
  }
  Print("ORTBO: Per-symbol MA/RSI indicator handles released.");

  // Release Single Global Instance Indicator Handles
  if(m_csiHandle != INVALID_HANDLE)
  {
    IndicatorRelease(m_csiHandle);
    Print("ORTBO: Global CSI handle released.");
    m_csiHandle = INVALID_HANDLE; // Reset handle
  }
  if(m_cffpHandle != INVALID_HANDLE)
  {
    IndicatorRelease(m_cffpHandle);
    Print("ORTBO: Global CFFP handle released.");
    m_cffpHandle = INVALID_HANDLE; // Reset handle
  }
  if(m_cmsmHandle != INVALID_HANDLE)
  {
    IndicatorRelease(m_cmsmHandle);
    Print("ORTBO: Global CMSM handle released.");
    m_cmsmHandle = INVALID_HANDLE; // Reset handle
  }

  // Deinit modules (GridEngine first, then EquityMonitor, then Display)
  g_gridEngine.OnDeinit(reason);
  g_equityMonitor.OnDeinit(reason);
  if(CheckPointer(GetPointer(g_display)) != POINTER_INVALID)
  {
    g_display.Destroy(reason);
    Print("ORTBO: Common Display destroyed.");
  }

  Print("ORTBO: Deinitialization complete.");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
  // --- Main Multi-Symbol Loop ---
  for(int SymbolLoop = 0; SymbolLoop < NumberOfTradeableSymbols; SymbolLoop++)
  {
     string CurrentSymbol = SymbolArray[SymbolLoop];
     // --- Calculate Indicator Signals for the Current Symbol ---
     Direction signalMA = GetSignalMA(SymbolLoop, CurrentSymbol);
     Direction signalRSI = GetSignalRSI(SymbolLoop, CurrentSymbol);
     Direction signalCSI = GetSignalCSI(SymbolLoop, CurrentSymbol);
     Direction signalCFFP = GetSignalCFFP(SymbolLoop, CurrentSymbol);
     Direction signalCMSM = GetSignalCMSM(SymbolLoop, CurrentSymbol);
     

     // --- Calculate Indices for this symbol's grids in the flat array ---
     int indexMach1 = SymbolLoop * NUMBER_OF_GRIDS_PER_SYMBOL + 0; // MODIFIED: Index calculation
     int indexMach2 = SymbolLoop * NUMBER_OF_GRIDS_PER_SYMBOL + 1; // MODIFIED: Index calculation
     int indexMach3 = SymbolLoop * NUMBER_OF_GRIDS_PER_SYMBOL + 2; // ADDED: Index calculation

     // --- Get Grid Pointers for this symbol ---
     GridInstance* targetGrid1 = NULL;
     if(ArraySize(g_gridInstances) > indexMach1) targetGrid1 = g_gridInstances[indexMach1];
     GridInstance* targetGrid2 = NULL;
     if(ArraySize(g_gridInstances) > indexMach2) targetGrid2 = g_gridInstances[indexMach2];
     GridInstance* targetGrid3 = NULL; // ADDED: targetGrid3
     if(ArraySize(g_gridInstances) > indexMach3) targetGrid3 = g_gridInstances[indexMach3];

     // --- NEW: Call ProcessGridLogic for each grid ---
     if(CheckPointer(targetGrid1) != POINTER_INVALID)
     {
        // Cast to Grid* because ProcessGridLogic expects the derived class pointer
        // MODIFIED: Pass CFFP signal
        ProcessGridLogic((Grid*)targetGrid1, signalMA, signalRSI, signalCSI, signalCFFP, signalCMSM);
     }
     else { PrintFormat("[ERROR] ORTBO: Invalid pointer for MACH1 grid, Symbol %s, Index %d", CurrentSymbol, indexMach1); }

     if(CheckPointer(targetGrid2) != POINTER_INVALID)
     {
        // MODIFIED: Pass CFFP signal
        ProcessGridLogic((Grid*)targetGrid2, signalMA, signalRSI, signalCSI, signalCFFP, signalCMSM);
     }
     else { PrintFormat("[ERROR] ORTBO: Invalid pointer for MACH2 grid, Symbol %s, Index %d", CurrentSymbol, indexMach2); }

     if(CheckPointer(targetGrid3) != POINTER_INVALID) // ADDED: Process MACH3
     {
         // MODIFIED: Pass CFFP signal
        ProcessGridLogic((Grid*)targetGrid3, signalMA, signalRSI, signalCSI, signalCFFP, signalCMSM);
     }
     else { PrintFormat("[ERROR] ORTBO: Invalid pointer for MACH3 grid, Symbol %s, Index %d", CurrentSymbol, indexMach3); }


  } // --- End of Multi-Symbol Loop ---


  // --- Logic outside the symbol loop (runs once per tick) ---

  // Call EquityMonitor OnTick - checks account-level limits
  g_equityMonitor.OnTick();
  // Tell grid engine if trading limit was reached by equity monitor
  g_gridEngine.AllowGridStart(!g_equityMonitor.LimitReached());
  // Run grid engine's tick processing (manages all grid instances)
  g_gridEngine.OnTick();
}

//+------------------------------------------------------------------+
//| Chart Event handler                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
   const long& lparam,
   const double& dparam,
   const string& sparam)
{
// Pass chart events to modules that might use them (like the Display)
if(CheckPointer(GetPointer(g_display)) != POINTER_INVALID) g_display.ChartEvent(id, lparam, dparam, sparam);
if(CheckPointer(GetPointer(g_gridEngine)) != POINTER_INVALID) g_gridEngine.OnChartEvent(id, lparam, dparam, sparam);
if(CheckPointer(GetPointer(g_equityMonitor)) != POINTER_INVALID) g_equityMonitor.OnChartEvent(id, lparam, dparam, sparam);
}

//+------------------------------------------------------------------+
//| Indicator Initialization Functions                               |
//+------------------------------------------------------------------+

int InitIndicators()
{
  // Resize and initialize per-symbol handles for MA and RSI
  ArrayResize(m_MAHandle, NumberOfTradeableSymbols);
  ArrayResize(m_RSIHandle, NumberOfTradeableSymbols);
  ArrayInitialize(m_MAHandle, INVALID_HANDLE);
  ArrayInitialize(m_RSIHandle, INVALID_HANDLE);

  // --- Initialize Single Instance Indicators (CSI, CFFP, CMSM) ---
  // These are loaded ONCE on the current chart the EA is attached to (_Symbol).
  // Their respective Inp...Frame inputs define their calculation timeframe.

  PrintFormat("ORTBO: Single instance indicators (CSI, CFFP, CMSM) will load on the EA's current chart symbol: %s", _Symbol);

  // Initialize CSI (once)
  if(InpEnableSignalCSI)
  {
    // Uses the EA's current chart symbol and the specific CSI timeframe input
    m_csiHandle = iCustom(_Symbol, InpCSIFrame, "::CURRENCY_STRENGTH_INDEX.ex5", InpCSIMAPeriod, InpCSIMADelta);
    if (m_csiHandle == INVALID_HANDLE)
    {
      PrintFormat("[ERROR] ORTBO: Failed to get single CSI handle on %s, TF %s. Error %d", _Symbol, EnumToString(InpCSIFrame), GetLastError());
      return INIT_FAILED; 
    }
    PrintFormat("ORTBO: Single CSI Handle created successfully (Handle: %d) on %s, Input TF %s.", m_csiHandle, _Symbol, EnumToString(InpCSIFrame));
  }

  // Initialize CFFP (once)
  if(InpEnableSignalCFFP)
  {
    // Uses the EA's current chart symbol and the specific CFFP timeframe input
    m_cffpHandle = iCustom(_Symbol, InpCFFPFrame, "::CCFp.ex5", InpCFFPFastMAPeriod, InpCFFPSlowMAPeriod, InpCFFPMAMethod, InpCFFPAppliedPrice);
    if (m_cffpHandle == INVALID_HANDLE)
    {
      PrintFormat("[ERROR] ORTBO: Failed to get single CFFP handle on %s, TF %s. Error %d", _Symbol, EnumToString(InpCFFPFrame), GetLastError());
      return INIT_FAILED; 
    }
    PrintFormat("ORTBO: Single CFFP Handle created successfully (Handle: %d) on %s, Input TF %s.", m_cffpHandle, _Symbol, EnumToString(InpCFFPFrame));
  }

  // Initialize CMSM (once - on the EA's current chart)
  if(InpEnableSignalCMSM) // Assuming input InpEnableSignalCMSW was renamed
  {
    // Uses the EA's current chart symbol and the specific CMSM timeframe input (previously InpCMSMTimeFrame)
    m_cmsmHandle = iCustom(_Symbol, InpCMSMTimeFrame, "::Indicators\\###CMSMIndV17.06 (1).ex5",
                             InpCMSM_TradeLevel, // Ensure these CMSM-specific inputs are correctly named
                             InpCMSMTimeFrame,    
                             InpCMSM_TimeIndex   
                             // ... ADD ALL OTHER REQUIRED PARAMS FOR CMSM.ex5 ...
                             );
    if (m_cmsmHandle == INVALID_HANDLE)
    {
       PrintFormat("[ERROR] ORTBO: Failed to get single CMSM handle on %s, TF %s. Error %d", _Symbol, EnumToString(InpCMSMTimeFrame), GetLastError());
       return INIT_FAILED;
    }
    PrintFormat("ORTBO: Single CMSM Handle created successfully (Handle: %d) on %s, Input TF %s.", m_cmsmHandle, _Symbol, EnumToString(InpCMSMTimeFrame));
  }
  // --- End Single Instance Initializations ---


  // --- Per-Symbol Mappings and Optimization Array Setups ---
  ArrayResize(m_BaseCurrencyIndex, NumberOfTradeableSymbols);
  ArrayResize(m_QuoteCurrencyIndex, NumberOfTradeableSymbols);
  ArrayResize(m_CFFPBaseCurrencyIndex, NumberOfTradeableSymbols);
  ArrayResize(m_CFFPQuoteCurrencyIndex, NumberOfTradeableSymbols);

  // Resize Optimization Arrays (still per-symbol for all indicators' signal tracking)
  ArrayResize(m_CSILastBarTime, NumberOfTradeableSymbols); ArrayResize(m_CSILastSignal, NumberOfTradeableSymbols);
  ArrayResize(m_CFFPLastBarTime, NumberOfTradeableSymbols); ArrayResize(m_CFFPLastSignal, NumberOfTradeableSymbols);
  ArrayResize(m_MALastBarTime, NumberOfTradeableSymbols); ArrayResize(m_MALastSignal, NumberOfTradeableSymbols);
  ArrayResize(m_RSILastBarTime, NumberOfTradeableSymbols); ArrayResize(m_RSILastSignal, NumberOfTradeableSymbols);
  ArrayResize(m_CMSMLastBarTime, NumberOfTradeableSymbols); ArrayResize(m_CMSMLastSignal, NumberOfTradeableSymbols); // Renamed

  // Initialize Optimization Arrays
  ArrayInitialize(m_CSILastBarTime, 0);  ArrayInitialize(m_CSILastSignal, None);
  ArrayInitialize(m_CFFPLastBarTime, 0); ArrayInitialize(m_CFFPLastSignal, None);
  ArrayInitialize(m_MALastBarTime, 0);   ArrayInitialize(m_MALastSignal, None);
  ArrayInitialize(m_RSILastBarTime, 0);  ArrayInitialize(m_RSILastSignal, None);
  ArrayInitialize(m_CMSMLastBarTime, 0); ArrayInitialize(m_CMSMLastSignal, None); // Renamed

  // Resize and set series for MA and RSI buffers (per-symbol indicators)
  ArrayResize(m_MABuffer, 1); ArrayResize(m_RSIBuffer, 1);
  ArraySetAsSeries(m_MABuffer, true); ArraySetAsSeries(m_RSIBuffer, true);

  // Loop through each symbol for per-symbol indicator initializations (MA, RSI) and currency mappings
  for(int i = 0; i < NumberOfTradeableSymbols; i++)
  {
     string symbol_to_trade = SymbolArray[i]; 
     bool success_per_symbol = true;

     // Map currency indices for CSI (uses symbol_to_trade)
     if(InpEnableSignalCSI && !MapCurrencyIndices(i, symbol_to_trade))
     { PrintFormat("[WARN] ORTBO: Failed to map CSI currency indices for %s. CSI signal may be unreliable.", symbol_to_trade); }

     // Map currency indices for CFFP (uses symbol_to_trade)
     if(InpEnableSignalCFFP && !MapCurrencyIndicesCFFP(i, symbol_to_trade))
     { PrintFormat("[WARN] ORTBO: Failed to map CFFP currency indices for %s. CFFP signal may be unreliable.", symbol_to_trade); }

     // Initialize MA (per-symbol)
     if(InpEnableSignalMovingAverage)
     { if(!InitMA(i, symbol_to_trade)) success_per_symbol = false; }

     // Initialize RSI (per-symbol)
     if(InpEnableSignalRSI)
     { if(!InitRSI(i, symbol_to_trade)) success_per_symbol = false; }
     
     if(!success_per_symbol) // Check if MA or RSI init failed for this specific symbol
     {
        PrintFormat("[ERROR] ORTBO: Per-symbol indicator initialization (MA/RSI) failed for %s. EA exiting.", symbol_to_trade);
        return INIT_FAILED;
     }
  }
  Print("ORTBO: All indicator initializations and mappings completed.");
  return INIT_SUCCEEDED;
}

// --- Initialize MA handle for a specific symbol index ---
bool InitMA(int symbolIndex, string symbol)
{
  // Check index validity
  if(symbolIndex < 0 || symbolIndex >= ArraySize(m_MAHandle))
  {
     PrintFormat("[ERROR] ORTBO: Invalid index %d for MA handle array (Size: %d)", symbolIndex, ArraySize(m_MAHandle));
     return false;
  }

  m_MAHandle[symbolIndex] = iMA(symbol, InpMaFrame, InpMaPeriod, InpMaShift, InpMaMethod, InpMaPrice);
  if (m_MAHandle[symbolIndex] == INVALID_HANDLE)
  {
    PrintFormat("[ERROR] ORTBO: Failed to get MA handle for %s on %s. Error %d", symbol, EnumToString(InpMaFrame), GetLastError());
    return false;
  }
  PrintFormat("ORTBO: MA Handle for %s created successfully (Handle: %d).", symbol, m_MAHandle[symbolIndex]);
  return true;
}

// --- Initialize RSI handle for a specific symbol index ---
bool InitRSI(int symbolIndex, string symbol)
{
  // Check index validity
  if(symbolIndex < 0 || symbolIndex >= ArraySize(m_RSIHandle))
  {
     PrintFormat("[ERROR] ORTBO: Invalid index %d for RSI handle array (Size: %d)", symbolIndex, ArraySize(m_RSIHandle));
     return false;
  }

  m_RSIHandle[symbolIndex] = iRSI(symbol, InpRSIFrame, InpRsiPeriod, PRICE_CLOSE);
  if (m_RSIHandle[symbolIndex] == INVALID_HANDLE)
  {
    PrintFormat("[ERROR] ORTBO: Failed to get RSI handle for %s on %s. Error %d", symbol, EnumToString(InpRSIFrame), GetLastError());
    return false;
  }
  PrintFormat("ORTBO: RSI Handle for %s created successfully (Handle: %d).", symbol, m_RSIHandle[symbolIndex]);
  return true;
}


// --- Map base/quote currency names to CSI buffer indices ---
bool MapCurrencyIndices(int symbolIndex, string symbol)
{
   string baseCcy = SymbolInfoString(symbol, SYMBOL_CURRENCY_BASE);
   if(baseCcy == NULL || baseCcy == "")
   {
      PrintFormat("[ERROR] ORTBO: Failed to get Base Currency for %s. Error: %d", symbol, GetLastError());
      m_BaseCurrencyIndex[symbolIndex] = -1;
      m_QuoteCurrencyIndex[symbolIndex] = -1;
      return false;
   }

   string quoteCcy = SymbolInfoString(symbol, SYMBOL_CURRENCY_PROFIT);
   if(quoteCcy == NULL || quoteCcy == "")
   {
      PrintFormat("[ERROR] ORTBO: Failed to get Quote Currency for %s. Error: %d", symbol, GetLastError());
      m_BaseCurrencyIndex[symbolIndex] = -1;
      m_QuoteCurrencyIndex[symbolIndex] = -1;
      return false;
   }

   m_BaseCurrencyIndex[symbolIndex] = -1; // Reset before searching
   m_QuoteCurrencyIndex[symbolIndex] = -1;

   for(int j=0; j<8; j++) // Loop through CSI currency names
   {
      if(m_CurrencyNames[j] == baseCcy) m_BaseCurrencyIndex[symbolIndex] = j;
      if(m_CurrencyNames[j] == quoteCcy) m_QuoteCurrencyIndex[symbolIndex] = j;
   }

   if(m_BaseCurrencyIndex[symbolIndex] < 0 || m_QuoteCurrencyIndex[symbolIndex] < 0)
   {
      PrintFormat("[WARN] ORTBO: Cannot map Base (%s) or Quote (%s) currency for symbol '%s' to CSI buffers. CSI Signal will be None.",
                  (m_BaseCurrencyIndex[symbolIndex] < 0) ? baseCcy : "-",
                  (m_QuoteCurrencyIndex[symbolIndex] < 0) ? quoteCcy : "-",
                  symbol);
      return false; // Indicate mapping failure (though EA continues)
   }
   return true;
}

// --- ADDED: Map base/quote currency names to CFFP buffer indices ---
bool MapCurrencyIndicesCFFP(int symbolIndex, string symbol)
{
   string baseCcy = SymbolInfoString(symbol, SYMBOL_CURRENCY_BASE);
   if(baseCcy == NULL || baseCcy == "")
   {
      PrintFormat("[ERROR] ORTBO CFFP Map: Failed to get Base Currency for %s. Error: %d", symbol, GetLastError());
      m_CFFPBaseCurrencyIndex[symbolIndex] = -1;
      m_CFFPQuoteCurrencyIndex[symbolIndex] = -1;
      return false;
   }

   string quoteCcy = SymbolInfoString(symbol, SYMBOL_CURRENCY_PROFIT);
   if(quoteCcy == NULL || quoteCcy == "")
   {
      PrintFormat("[ERROR] ORTBO CFFP Map: Failed to get Quote Currency for %s. Error: %d", symbol, GetLastError());
      m_CFFPBaseCurrencyIndex[symbolIndex] = -1;
      m_CFFPQuoteCurrencyIndex[symbolIndex] = -1;
      return false;
   }

   m_CFFPBaseCurrencyIndex[symbolIndex] = -1; // Reset before searching
   m_CFFPQuoteCurrencyIndex[symbolIndex] = -1;

   // Loop through CFFP currency names (different order!)
   for(int j=0; j<8; j++)
   {
      if(m_CFFPCurrencyNames[j] == baseCcy) m_CFFPBaseCurrencyIndex[symbolIndex] = j;
      if(m_CFFPCurrencyNames[j] == quoteCcy) m_CFFPQuoteCurrencyIndex[symbolIndex] = j;
   }

   if(m_CFFPBaseCurrencyIndex[symbolIndex] < 0 || m_CFFPQuoteCurrencyIndex[symbolIndex] < 0)
   {
      PrintFormat("[WARN] ORTBO CFFP Map: Cannot map Base (%s) or Quote (%s) currency for symbol '%s' to CFFP buffers. CFFP Signal will be None.",
                  (m_CFFPBaseCurrencyIndex[symbolIndex] < 0) ? baseCcy : "-",
                  (m_CFFPQuoteCurrencyIndex[symbolIndex] < 0) ? quoteCcy : "-",
                  symbol);
      return false; // Indicate mapping failure
   }
    // PrintFormat("[DEBUG] ORTBO CFFP Map: Mapped %s: Base '%s' (Index %d), Quote '%s' (Index %d)",
    //             symbol, baseCcy, m_CFFPBaseCurrencyIndex[symbolIndex], quoteCcy, m_CFFPQuoteCurrencyIndex[symbolIndex]);
   return true;
}


//+------------------------------------------------------------------+
//| Indicator Signal Calculation Functions                           |
//+------------------------------------------------------------------+

// --- Get MA signal for a specific symbol index (Updated for Once Per Bar) ---
Direction GetSignalMA(int symbolIndex, string symbol)
{
  // Check if signal enabled and handle is valid for this index
  if (!InpEnableSignalMovingAverage || symbolIndex < 0 || symbolIndex >= ArraySize(m_MAHandle) || m_MAHandle[symbolIndex] == INVALID_HANDLE)
     return None;

  // --- NEW: Per-Bar Calculation Logic ---
  datetime currentIndyBarTime = iTime(symbol, InpMaFrame, 0); // Get current bar time on MA's timeframe
  if(currentIndyBarTime == 0) // Error getting bar time
  {
       PrintFormat("[ERROR] ORTBO: Failed to get current bar time for %s on %s (MA). Returning last signal.", symbol, EnumToString(InpMaFrame));
       return m_MALastSignal[symbolIndex]; // Return last known signal on error
  }

  if(currentIndyBarTime <= m_MALastBarTime[symbolIndex])
  {
       // Not a new bar on MA's timeframe, return the previously stored signal
       return m_MALastSignal[symbolIndex];
  }
  // It's a NEW BAR - Proceed with calculation
  m_MALastBarTime[symbolIndex] = currentIndyBarTime; // Store new bar time
  // --- END NEW ---

  ResetLastError();
  int copied = CopyBuffer(m_MAHandle[symbolIndex], 0, 1, 1, m_MABuffer); // Get MA value of the previous completed bar (index 1)

  if (copied <= 0)
  {
    PrintFormat("[ERROR] ORTBO: Failed to copy MA buffer for %s. Copied: %d, Error: %d", symbol, copied, GetLastError());
    m_MALastSignal[symbolIndex] = None; // Store None for this new bar time as calc failed
    return None;
  }
  double maValue = m_MABuffer[0];

  double closePrice = iClose(symbol, InpMaFrame, 1); // Get the close price of the previous completed bar on the MA's timeframe
  if(closePrice == 0)
  {
     PrintFormat("[WARN] ORTBO: Failed to get previous close price for %s on %s. MA Signal unreliable.", symbol, EnumToString(InpMaFrame));
     m_MALastSignal[symbolIndex] = None; // Store None
     return None;
  }

  double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
  if(point <= 0)
  {
     PrintFormat("[ERROR] ORTBO: Failed to get valid point value (%.10f) for %s. MA Signal unreliable.", point, symbol);
     m_MALastSignal[symbolIndex] = None; // Store None
     return None;
  }

  Direction signal = None;
  //if (iClose(_Symbol, PERIOD_CURRENT, 0) + (InpMaMargin * _Point) > m_MABuffer[0]) signal = Buy;
  //if (closePrice > maValue + (InpMaMargin * point)) signal = Buy;
  //if (closePrice < maValue - (InpMaMargin * point)) signal = Sell;
  
  if (closePrice + (InpMaMargin * point) > maValue ) signal = Buy;
  if (closePrice - (InpMaMargin * point) < maValue ) signal = Sell;

  if (InpMaFilterInverter)
  {
     if (signal == Buy) signal = Sell;
     else if (signal == Sell) signal = Buy;
  }

  m_MALastSignal[symbolIndex] = signal; // Store the calculated signal
  return signal;
}

// --- Get RSI signal for a specific symbol index (Updated for Once Per Bar) ---
Direction GetSignalRSI(int symbolIndex, string symbol)
{
  // Check if signal enabled and handle is valid for this index
  if (!InpEnableSignalRSI || symbolIndex < 0 || symbolIndex >= ArraySize(m_RSIHandle) || m_RSIHandle[symbolIndex] == INVALID_HANDLE)
     return None;

  // --- NEW: Per-Bar Calculation Logic ---
  datetime currentIndyBarTime = iTime(symbol, InpRSIFrame, 0); // Get current bar time on RSI's timeframe
  if(currentIndyBarTime == 0) // Error getting bar time
  {
       PrintFormat("[ERROR] ORTBO: Failed to get current bar time for %s on %s (RSI). Returning last signal.", symbol, EnumToString(InpRSIFrame));
       return m_RSILastSignal[symbolIndex]; // Return last known signal on error
  }

  if(currentIndyBarTime <= m_RSILastBarTime[symbolIndex])
  {
       // Not a new bar on RSI's timeframe, return the previously stored signal
       return m_RSILastSignal[symbolIndex];
  }
  // It's a NEW BAR - Proceed with calculation
  m_RSILastBarTime[symbolIndex] = currentIndyBarTime; // Store new bar time
  // --- END NEW ---

  ResetLastError();
  int copied = CopyBuffer(m_RSIHandle[symbolIndex], 0, 1, 1, m_RSIBuffer); // Get RSI value for the previous completed bar (index 1)
  if (copied <= 0)
  {
    PrintFormat("[ERROR] ORTBO: Failed to copy RSI buffer for %s. Copied: %d, Error: %d", symbol, copied, GetLastError());
    m_RSILastSignal[symbolIndex] = None; // Store None for this new bar time as calc failed
    return None;
  }
  double rsiValue = m_RSIBuffer[0];

  double prevClose = iClose(symbol, PERIOD_CURRENT, 2);
  double currClose = iClose(symbol, PERIOD_CURRENT, 1);

   Direction signal = None;

   // Prioritized pure RSI conditions
   if (m_RSIBuffer[0] < InpRsiMinimum) {
       signal = Buy;
   }
   else if (m_RSIBuffer[0] > InpRsiMaximum) {
       signal = Sell;
   }
   // Only check price+RSI conditions if pure RSI conditions weren't met
   else { /*
       if (prevClose > currClose && m_RSIBuffer[0] > InpRsiMinimum) {
           signal = Buy;
       }
       else if (prevClose < currClose && m_RSIBuffer[0] < InpRsiMaximum) {
           signal = Sell;
       } */
   }
     
  if (InpRSIFilterInverter)
  {
    if (signal == Buy) signal = Sell;
    else if (signal == Sell) signal = Buy;
  }

  m_RSILastSignal[symbolIndex] = signal; // Store the calculated signal
  return signal;
}

/*
// --- Get CSI signal for a specific symbol index (Uses single global handle) ---
Direction GetSignalCSI(int symbolIndex, string symbol)
{
   if (!InpEnableSignalCSI || m_csiHandle == INVALID_HANDLE || symbolIndex < 0 || symbolIndex >= NumberOfTradeableSymbols)
      return None;

   int baseIdx = m_BaseCurrencyIndex[symbolIndex];
   int quoteIdx = m_QuoteCurrencyIndex[symbolIndex];
   if(baseIdx < 0 || quoteIdx < 0) return None;
      
   // iTime check is for the 'symbol' on the CSI's operating timeframe (InpCSIFrame)
   datetime currentIndyBarTime = iTime(symbol, InpCSIFrame, 0);
   if(currentIndyBarTime == 0) 
   {
       PrintFormat("[ERROR] ORTBO GetSignalCSI: Failed to get bar time for %s on %s. Last sig: %s", symbol, EnumToString(InpCSIFrame), EnumToString(m_CSILastSignal[symbolIndex]));
       return m_CSILastSignal[symbolIndex]; 
   }

   if(currentIndyBarTime <= m_CSILastBarTime[symbolIndex])
   { return m_CSILastSignal[symbolIndex]; }

   m_CSILastBarTime[symbolIndex] = currentIndyBarTime;
   
   if(BarsCalculated(m_csiHandle) < 2)
   { m_CSILastSignal[symbolIndex] = None; return None; }

   double strengths[8]; // Already statically sized, ArrayResize not needed
   double tempBuffer[1];
   bool calculation_ok = true;

   for(int j = 0; j < 8; j++)
   {
      ResetLastError();
      int copied = CopyBuffer(m_csiHandle, j, 1, 1, tempBuffer); 
      if (copied <= 0)
      {
         PrintFormat("[ERROR] ORTBO GetSignalCSI: Failed to copy CSI buffer %d (%s) for sym %s. Handle %d. Copied %d, Err %d",
                     j, m_CurrencyNames[j], symbol, m_csiHandle, copied, GetLastError());
         calculation_ok = false; break;
      }
      strengths[j] = tempBuffer[0];
   }

   Direction signal = None;
   if(calculation_ok)
   {
       double maxStrength = -DBL_MAX; int maxIndex = -1;
       double minStrength = DBL_MAX; int minIndex = -1;
       for(int j = 0; j < 8; j++)
       {
          if(strengths[j] > maxStrength) { maxStrength = strengths[j]; maxIndex = j; }
          if(strengths[j] < minStrength) { minStrength = strengths[j]; minIndex = j; }
       }
       double prevClose = iClose(symbol, PERIOD_CURRENT, 2); 
       double currClose = iClose(symbol, PERIOD_CURRENT, 1);
       if(prevClose != 0 && currClose != 0)
       {
          if (prevClose < currClose && baseIdx == maxIndex && quoteIdx == minIndex) signal = Sell;
          else if (prevClose > currClose && baseIdx == minIndex && quoteIdx == maxIndex) signal = Buy;
       }
       else
       {
          PrintFormat("[WARN] ORTBO GetSignalCSI: No close prices for confirm on %s. Pure strength signal.", symbol);
          if (baseIdx == maxIndex && quoteIdx == minIndex) signal = Sell;
          else if (baseIdx == minIndex && quoteIdx == maxIndex) signal = Buy;
       }
       if (InpCSIFilterInverter)
       { if (signal == Buy) signal = Sell; else if (signal == Sell) signal = Buy; }
   } 
   m_CSILastSignal[symbolIndex] = signal;
   return signal;
}


// --- Get CFFP signal for a specific symbol index (Uses single global handle) ---
Direction GetSignalCFFP(int symbolIndex, string symbol)
{
   if (!InpEnableSignalCFFP || m_cffpHandle == INVALID_HANDLE || symbolIndex < 0 || symbolIndex >= NumberOfTradeableSymbols)
      return None;
   int baseIdx = m_CFFPBaseCurrencyIndex[symbolIndex];
   int quoteIdx = m_CFFPQuoteCurrencyIndex[symbolIndex];
   if(baseIdx < 0 || quoteIdx < 0) return None;
      
   // iTime check is for the 'symbol' on the CFFP's operating timeframe (InpCFFPFrame)
   datetime currentIndyBarTime = iTime(symbol, InpCFFPFrame, 0);
   if(currentIndyBarTime == 0)
   {
       PrintFormat("[ERROR] ORTBO GetSignalCFFP: Failed to get bar time for %s on %s. Last sig: %s", symbol, EnumToString(InpCFFPFrame), EnumToString(m_CFFPLastSignal[symbolIndex]));
       return m_CFFPLastSignal[symbolIndex];
   }
   if(currentIndyBarTime <= m_CFFPLastBarTime[symbolIndex])
   { return m_CFFPLastSignal[symbolIndex]; }

   m_CFFPLastBarTime[symbolIndex] = currentIndyBarTime;
   
   if(BarsCalculated(m_cffpHandle) < 2)
   { m_CFFPLastSignal[symbolIndex] = None; return None; }

   double strengths[8]; // Already statically sized, ArrayResize not needed
   double tempBuffer[1];
   bool calculation_ok = true;
   for(int j = 0; j < 8; j++)
   {
      ResetLastError();
      int copied = CopyBuffer(m_cffpHandle, j, 1, 1, tempBuffer);
      if (copied <= 0)
      {
         PrintFormat("[ERROR] ORTBO GetSignalCFFP: Failed to copy CFFP buffer %d (%s) for sym %s. Handle %d. Copied %d, Err %d",
                     j, m_CFFPCurrencyNames[j], symbol, m_cffpHandle, copied, GetLastError());
         calculation_ok = false; break;
      }
      strengths[j] = tempBuffer[0];
   }
   Direction signal = None;
   if(calculation_ok)
   {
        double sortedValues[8]; ArrayCopy(sortedValues, strengths, 0, 0, 8);
        int n = 8; for(int i = 0; i < n - 1; i++) { for(int k = 0; k < n - i - 1; k++) { if(sortedValues[k] > sortedValues[k + 1]) { double tempVal = sortedValues[k]; sortedValues[k] = sortedValues[k+1]; sortedValues[k+1] = tempVal;}}}
        double secondWeakestValue = sortedValues[1]; double secondStrongestValue = sortedValues[6];
        double baseStrength = strengths[baseIdx]; double quoteStrength = strengths[quoteIdx];
        bool baseIsStrong = (baseStrength >= secondStrongestValue); bool baseIsWeak = (baseStrength <= secondWeakestValue);
        bool quoteIsStrong= (quoteStrength >= secondStrongestValue); bool quoteIsWeak = (quoteStrength <= secondWeakestValue);
        if (baseIsStrong && quoteIsWeak) signal = Buy; else if (baseIsWeak && quoteIsStrong) signal = Sell;
        if (InpCFFPFilterInverter && signal != None) { signal = (signal == Buy) ? Sell : Buy; }
   } 
   m_CFFPLastSignal[symbolIndex] = signal;
   return signal; 
}
*/

// --- Get CSI signal for a specific symbol index (Uses single global handle) ---
// Incorporates "Original CSI Logic" and the detailed "S/W CSI Logic" 
// with prioritization and price confirmation for both.
Direction GetSignalCSI(int symbolIndex, string symbol)
{
    // Initial checks (unchanged)
    if (!InpEnableSignalCSI || m_csiHandle == INVALID_HANDLE || symbolIndex < 0 || symbolIndex >= NumberOfTradeableSymbols)
        return None;

    int baseIdx = m_BaseCurrencyIndex[symbolIndex];
    int quoteIdx = m_QuoteCurrencyIndex[symbolIndex];
    if(baseIdx < 0 || quoteIdx < 0) return None;
      
    // "Once Per Bar" logic (unchanged)
    datetime currentIndyBarTime = iTime(symbol, InpCSIFrame, 0);
    if(currentIndyBarTime == 0) 
    {
        PrintFormat("[ERROR] ORTBO GetSignalCSI: Failed to get bar time for %s on %s. Last sig: %s", symbol, EnumToString(InpCSIFrame), EnumToString(m_CSILastSignal[symbolIndex]));
        return m_CSILastSignal[symbolIndex]; 
    }

    if(currentIndyBarTime <= m_CSILastBarTime[symbolIndex])
    { return m_CSILastSignal[symbolIndex]; }

    m_CSILastBarTime[symbolIndex] = currentIndyBarTime;
    
    if(BarsCalculated(m_csiHandle) < 2)
    { m_CSILastSignal[symbolIndex] = None; return None; }

    // Data Retrieval (unchanged)
    double strengths[8]; 
    double tempBuffer[1];
    bool calculation_ok = true;

    for(int j = 0; j < 8; j++)
    {
        ResetLastError();
        int copied = CopyBuffer(m_csiHandle, j, 1, 1, tempBuffer); 
        if (copied <= 0)
        {
            PrintFormat("[ERROR] ORTBO GetSignalCSI: Failed to copy CSI buffer %d (%s) for sym %s. Handle %d. Copied %d, Err %d",
                        j, m_CurrencyNames[j], symbol, m_csiHandle, copied, GetLastError());
            calculation_ok = false; break;
        }
        strengths[j] = tempBuffer[0];
    }

    Direction final_signal = None; // Initialize the signal to be returned
    double prevClose = iClose(symbol, PERIOD_CURRENT, 2); 
    double currClose = iClose(symbol, PERIOD_CURRENT, 1);

    if(calculation_ok)
    {
        // Determine overall strongest (maxIndex) and weakest (minIndex) currencies
        double maxStrength = -DBL_MAX; int maxIndex = -1; 
        double minStrength = DBL_MAX; int minIndex = -1; 
        for(int j = 0; j < 8; j++)
        {
            if(strengths[j] > maxStrength) { maxStrength = strengths[j]; maxIndex = j; }
            if(strengths[j] < minStrength) { minStrength = strengths[j]; minIndex = j; }
        }
        //prevClose > currClose &&
        //prevClose < currClose &&
        // Get close prices for confirmation
        double prevClose = iClose(symbol, PERIOD_CURRENT, 2); 
        double currClose = iClose(symbol, PERIOD_CURRENT, 1);
        bool prices_valid = (prevClose != 0 && currClose != 0);

        // --- 1. Original CSI Logic (Prioritized) ---
        Direction signal_original = None;
        if(prices_valid)
        {
            // Base is strongest, Quote is weakest, price confirms Sell (uptrend for sell)
            if ( prevClose < currClose && baseIdx == maxIndex && quoteIdx == minIndex) 
                signal_original = Sell; 
            // Base is weakest, Quote is strongest, price confirms Buy (downtrend for buy)
            else if (prevClose > currClose && baseIdx == minIndex && quoteIdx == maxIndex) 
                signal_original = Buy; 
        }
        else // No valid prices for confirmation in Original Logic
        {
            PrintFormat("[WARN] ORTBO GetSignalCSI (Original Logic): No close prices for confirm on %s. Pure strength signal for Original.", symbol);
            if ( prevClose < currClose && baseIdx == maxIndex && quoteIdx == minIndex) 
                signal_original = Sell;
            else if ( prevClose > currClose && baseIdx == minIndex && quoteIdx == maxIndex) 
                signal_original = Buy;
        }

        // --- Assign signal based on priority ---
        if (signal_original != None)
        {
            final_signal = signal_original;
        }
        else // Original Logic yielded None, try S/W Logic
        {
        //prevClose > currClose &&
        //prevClose < currClose &&
            // --- 2. S/W (Strongest vs. Weakest) CSI Logic --- 
            /*
            Direction signal_sw_with_price_confirm = None; // Final S/W signal after price confirmation
            Direction sig_sw_strength_based = None;    // S/W signal based purely on strength conditions

            // Scenario 1: Based on the universally STRONGEST currency (maxIndex)
            if (prevClose > currClose && quoteIdx == maxIndex && strengths[baseIdx] < 0) {
                sig_sw_strength_based = Buy; // Expect strongest Quote to weaken, or weak (sub-zero) Base to rise
            } else if (prevClose < currClose && baseIdx == maxIndex && strengths[quoteIdx] < 0) {
                sig_sw_strength_based = Sell; // Expect strongest Base to weaken, or weak (sub-zero) Quote to rise
            }
            // Scenario 2: Based on the universally WEAKEST currency (minIndex)
            // Evaluated only if Scenario 1 did not produce a signal
            else if (prevClose > currClose && baseIdx == minIndex && strengths[quoteIdx] > 0) {
                sig_sw_strength_based = Buy; // Expect weakest Base to strengthen, or strong (super-zero) Quote to fall
            } else if (prevClose < currClose && quoteIdx == minIndex && strengths[baseIdx] > 0) {
                sig_sw_strength_based = Sell; // Expect weakest Quote to strengthen, or strong (super-zero) Base to fall
            }

            // Apply price confirmation to the strength-based S/W signal
            if (sig_sw_strength_based != None) 
            {
                if (prices_valid) 
                {
                    // S/W Buy AND price confirms Buy (downtrend for buy)
                    if (sig_sw_strength_based == Buy && prevClose > currClose) { 
                        signal_sw_with_price_confirm = Buy;
                    } 
                    // S/W Sell AND price confirms Sell (uptrend for sell)
                    else if (sig_sw_strength_based == Sell && prevClose < currClose) { 
                        signal_sw_with_price_confirm = Sell;
                    }
                    // If price confirmation fails, signal_sw_with_price_confirm remains None.
                }
                else // No valid prices for S/W confirmation, use pure S/W strength signal
                {
                    PrintFormat("[WARN] ORTBO GetSignalCSI (S/W Logic): No close prices for S/W confirm on %s. Pure S/W strength signal.", symbol);
                    signal_sw_with_price_confirm = sig_sw_strength_based;
                }
            }
            final_signal = signal_sw_with_price_confirm; // Assign S/W signal (could be None) */
        }

        // Apply common filter inverter to the final determined signal (unchanged)
        if (InpCSIFilterInverter && final_signal != None)
        {
            final_signal = (final_signal == Buy) ? Sell : Buy; 
        }
    } 
    
    // Store and return the final signal (unchanged)
    m_CSILastSignal[symbolIndex] = final_signal;
    return final_signal;
}

// --- Get CFFP signal for a specific symbol index (Handles BOTH logics internally) ---
Direction GetSignalCFFP(int symbolIndex, string symbol)
{
 double prevClose = iClose(symbol, PERIOD_CURRENT, 2); 
        double currClose = iClose(symbol, PERIOD_CURRENT, 1);
   // Basic checks
   if (!InpEnableSignalCFFP || m_cffpHandle == INVALID_HANDLE || symbolIndex < 0 || symbolIndex >= NumberOfTradeableSymbols)
      return None;

   int baseCFFPIdx = m_CFFPBaseCurrencyIndex[symbolIndex];
   int quoteCFFPIdx = m_CFFPQuoteCurrencyIndex[symbolIndex];
   if(baseCFFPIdx < 0 || quoteCFFPIdx < 0) return None;
      
   // --- "Once Per Bar" Logic (uses the single m_CFFPLastBarTime/Signal set) ---
   datetime currentIndyBarTime = iTime(symbol, InpCFFPFrame, 0);
   if(currentIndyBarTime == 0) {
       PrintFormat("[ERROR] ORTBO GetSignalCFFP: Failed to get bar time for %s on %s. Last sig: %s", symbol, EnumToString(InpCFFPFrame), EnumToString(m_CFFPLastSignal[symbolIndex]));
       return m_CFFPLastSignal[symbolIndex]; 
   }

   if(currentIndyBarTime <= m_CFFPLastBarTime[symbolIndex]) {
       return m_CFFPLastSignal[symbolIndex]; 
   }
   m_CFFPLastBarTime[symbolIndex] = currentIndyBarTime;
   
   if(BarsCalculated(m_cffpHandle) < 2) {
      m_CFFPLastSignal[symbolIndex] = None;
      return None; 
   }

   // --- Data Retrieval (Common for both logics) ---
   double strengths[8]; // Static size array
   double tempBuffer[1];
   bool calculation_ok = true;
   for(int j = 0; j < 8; j++) {
      ResetLastError();
      int copied = CopyBuffer(m_cffpHandle, j, 1, 1, tempBuffer);
      if (copied <= 0) {
         PrintFormat("[ERROR] ORTBO GetSignalCFFP: Failed to copy CFFP buffer %d (%s) for sym %s. Handle %d. Copied %d, Err %d",
                     j, m_CFFPCurrencyNames[j], symbol, m_cffpHandle, copied, GetLastError());
         calculation_ok = false; break;
      }
      strengths[j] = tempBuffer[0];
   }

   Direction final_signal = None; // The ultimate signal to be returned

   if(calculation_ok) {
        // --- Logic 1: Absolute Value (base > 0 && quote < 0) ---
        Direction sig_abs = None;
        double baseStrength_abs  = strengths[baseCFFPIdx];   
        double quoteStrength_abs = strengths[quoteCFFPIdx];
        if (prevClose > currClose && baseStrength_abs > 0 && quoteStrength_abs < 0) sig_abs = Buy;
        else if (prevClose < currClose && baseStrength_abs < 0 && quoteStrength_abs > 0) sig_abs = Sell;

        // --- Logic 2: Strongest vs Weakest Currency ---
        Direction sig_sw = None;
        double maxStrengthValue = -DBL_MAX; int strongestCcyBufferIdx = -1;
        double minStrengthValue = DBL_MAX;  int weakestCcyBufferIdx = -1;

        for(int j = 0; j < 8; j++) { // Find strongest/weakest from all 8 CFFP currencies
            if(strengths[j] > maxStrengthValue) { maxStrengthValue = strengths[j]; strongestCcyBufferIdx = j; }
            if(strengths[j] < minStrengthValue) { minStrengthValue = strengths[j]; weakestCcyBufferIdx = j; }
        }

        if (strongestCcyBufferIdx != -1 && weakestCcyBufferIdx != -1 && strongestCcyBufferIdx != weakestCcyBufferIdx) {
            if (prevClose > currClose && baseCFFPIdx == strongestCcyBufferIdx) sig_sw = Buy;      // Pair's Base is a_strongest
            else if (prevClose > currClose && quoteCFFPIdx == weakestCcyBufferIdx) sig_sw = Buy;  // Pair's Quote is a_weakest
            else if (prevClose < currClose && baseCFFPIdx == weakestCcyBufferIdx) sig_sw = Sell;   // Pair's Base is a_weakest
            else if (prevClose < currClose && quoteCFFPIdx == strongestCcyBufferIdx) sig_sw = Sell;// Pair's Quote is a_strongest
        }

        // --- Combine Signals (Priority to Absolute Value Logic) ---
        // If sig_abs gives a signal, use it. Otherwise, use sig_sw's signal (if any).
        if (sig_abs != None) {
            final_signal = sig_abs;
        } else { // sig_abs is None, so use whatever sig_sw gave (which might also be None)
            final_signal = sig_sw;
        }
        // Note: This combination logic assumes no conflict (sig_abs != sig_sw when both are non-None) as per your input.

        // Apply common filter inverter to the final determined signal
        if (InpCFFPFilterInverter && final_signal != None) {
            final_signal = (final_signal == Buy) ? Sell : Buy; 
        }
   } 
   
   m_CFFPLastSignal[symbolIndex] = final_signal; // Store the final combined/prioritized signal
   return final_signal; 
}

// --- Get CMSM Signal by reading chart label object (Renamed, Uses single global handle for load-check) ---
Direction GetSignalCMSM(int symbolIndex, string symbol) // <-- RENAMED
{
   // Renamed InpEnableSignalCMSM to InpEnableSignalCMSM
   if (!InpEnableSignalCMSM || m_cmsmHandle == INVALID_HANDLE || symbolIndex < 0 || symbolIndex >= NumberOfTradeableSymbols)
       return None;

   // iTime check is for the 'symbol' on the CMSM's assumed update timeframe (InpCMSMTimeFrame)
   // Renamed InpCMSM17TimeFrame to InpCMSMTimeFrame
   datetime currentIndyBarTime = iTime(symbol, InpCMSMTimeFrame, 0); 

   if(currentIndyBarTime == 0) 
   {
       PrintFormat("[ERROR] ORTBO GetSignalCMSM: Failed to get bar time for %s on %s. Last sig: %s", symbol, EnumToString(InpCMSMTimeFrame), EnumToString(m_CMSMLastSignal[symbolIndex])); // Renamed array
       return m_CMSMLastSignal[symbolIndex]; // Renamed array
   }

   // Renamed m_CMSMLastBarTime and m_CMSMLastSignal
   if(currentIndyBarTime <= m_CMSMLastBarTime[symbolIndex])
   { return m_CMSMLastSignal[symbolIndex]; }

   m_CMSMLastBarTime[symbolIndex] = currentIndyBarTime; // Renamed array
   
   Direction signal = None; 
   long chart_id = ChartID(); 
   int totalObjects = (int)ObjectsTotal(chart_id, -1, OBJ_LABEL); 
   string searchPatternCore = "_CMSM_Label_Suggestions_" + symbol + "_"; // Assuming label prefix remains _CMSM_

   for(int i = totalObjects - 1; i >= 0; i--) 
   {
      string objName = ObjectName(chart_id, i, -1, OBJ_LABEL); 
      if(StringFind(objName, searchPatternCore) >= 0) 
      {
         ResetLastError(); string labelText = ObjectGetString(chart_id, objName, OBJPROP_TEXT); int error_code = GetLastError();
         if(error_code == 0 && labelText != "") 
         {
            // Normalize text for easier comparison (case-insensitive)
               StringToUpper(labelText);
               string upperSymbol = symbol; // Get the symbol we are currently checking in ORTBO
               StringToUpper(upperSymbol);
            if(StringFind(labelText, "BUY") >= 0 && StringFind(labelText, upperSymbol) >= 0) { signal = Buy; break; }
            else if(StringFind(labelText, "SELL") >= 0 && StringFind(labelText, upperSymbol) >= 0) { signal = Sell; break; }
         }
         else if(error_code != 0) { PrintFormat("ORTBO GetSignalCMSM: Err reading obj '%s' for sym %s. Err: %d", objName, symbol, error_code); }
      } 
   } 
   // Renamed InpCMSMFilterInverter to InpCMSMFilterInverter (you'll need to adjust this input name too)
   if (InpCMSMFilterInverter) // <-- Potential input name change needed
   { if (signal == Buy) signal = Sell; else if (signal == Sell) signal = Buy; }

   m_CMSMLastSignal[symbolIndex] = signal; // Renamed array
   return signal;
}

// Update calls to GetSignalCMSM in your EA's logic (e.g., in OnTick, ProcessGridLogic, GetSignal)
// Example: Direction signalCMSM = GetSignalCMSM(SymbolLoop, CurrentSymbol);
// In GetSignal function:
// case SIGNAL_SRC_CMSM: // <-- ENUM change needed in ORTBO_Enums.mqh
//    return GetSignalCMSM(symbolIndex, symbol);

//+------------------------------------------------------------------+
//| Get Signal State for a Specific Source                           |
//+------------------------------------------------------------------+
Direction GetSignal(const int symbolIndex, ENUM_SIGNAL_SOURCE source)
{
   if(symbolIndex < 0 || symbolIndex >= NumberOfTradeableSymbols)
   {
      PrintFormat("ORTBO Error: Invalid symbolIndex %d in GetSignal.", symbolIndex);
      return None;
   }

   string symbol = SymbolArray[symbolIndex];
   if(symbol == "" || symbol == NULL)
   {
       PrintFormat("ORTBO Error: Invalid symbol string for symbolIndex %d in GetSignal.", symbolIndex);
       return None;
   }

   // Fetch individual signals
   Direction sigMA   = GetSignalMA(symbolIndex, symbol);
   Direction sigRSI  = GetSignalRSI(symbolIndex, symbol);    // Entry Signal 1
   Direction sigCSI  = GetSignalCSI(symbolIndex, symbol);    // Entry Signal 2
   Direction sigCFFP = GetSignalCFFP(symbolIndex, symbol);   // Trend Signal 1
   Direction sigCMSM = GetSignalCMSM(symbolIndex, symbol);   // Trend Signal 2

   // --- Handle Single Sources ---
   if (source == SIGNAL_SRC_NONE)   return None;
   if (source == SIGNAL_SRC_MA)     return sigMA;
   if (source == SIGNAL_SRC_RSI)    return sigRSI;
   if (source == SIGNAL_SRC_CSI)    return sigCSI;
   if (source == SIGNAL_SRC_CFFP)   return sigCFFP;
   if (source == SIGNAL_SRC_CMSM)   return sigCMSM;

   // --- Handle Existing MA Combinations (These remain untouched as requested) ---
   if (source == SIGNAL_SRC_MA_AND_RSI)
   {
      if (sigMA == Buy && sigRSI == Buy) return Buy;
      if (sigMA == Sell && sigRSI == Sell) return Sell;
      return None;
   }
   if (source == SIGNAL_SRC_MA_AND_CSI)
   {
      if (sigMA == Buy && sigCSI == Buy) return Buy;
      if (sigMA == Sell && sigCSI == Sell) return Sell;
      return None;
   }
   if (source == SIGNAL_SRC_MA_AND_CFFP)
   {
      if (sigMA == Buy && sigCFFP == Buy) return Buy;
      if (sigMA == Sell && sigCFFP == Sell) return Sell;
      return None;
   }

   // Add any other existing MA combinations here if you have them...

   // --- Handle other Existing 2-Way Non-MA Combinations (if any you want to keep explicitly) ---
   if (source == SIGNAL_SRC_RSI_AND_CSI) // 2 Entry
   {
      if (sigRSI == Buy && sigCSI == Buy) return Buy;
      if (sigRSI == Sell && sigCSI == Sell) return Sell;
      return None;
   }
   if (source == SIGNAL_SRC_RSI_AND_CFFP)
   {
      if (sigRSI == Buy && sigCFFP == Buy) return Buy;
      if (sigRSI == Sell && sigCFFP == Sell) return Sell;
      return None;
   }

   if (source == SIGNAL_SRC_CSI_AND_CFFP)
   {
      if (sigCSI == Buy && sigCFFP == Buy) return Buy;
      if (sigCSI == Sell && sigCFFP == Sell) return Sell;
      return None;
   }



   // --- NEW: Combinations (2 Entry + 1 Trend) ---
   if (source == SIGNAL_SRC_RSI_CSI_AND_CFFP) // (RSI & CSI) & CFFP
   {
      bool entrySignalBuy = (sigRSI == Buy && sigCSI == Buy);
      bool entrySignalSell = (sigRSI == Sell && sigCSI == Sell);
      
      if (entrySignalBuy && sigCFFP == Buy) return Buy;
      if (entrySignalSell && sigCFFP == Sell) return Sell;
      return None;
   }
   if (source == SIGNAL_SRC_RSI_CSI_AND_CMSM) // (RSI & CSI) & CMSM
   {
      bool entrySignalBuy = (sigRSI == Buy && sigCSI == Buy);
      bool entrySignalSell = (sigRSI == Sell && sigCSI == Sell);

      if (entrySignalBuy && sigCMSM == Buy) return Buy;
      if (entrySignalSell && sigCMSM == Sell) return Sell;
      return None;
   }

   // --- NEW: Combinations (2 Trend + 1 Entry) ---
   if (source == SIGNAL_SRC_CFFP_CMSM_AND_RSI) // (CFFP & CMSM) & RSI
   {
      bool trendSignalBuy = (sigCFFP == Buy && sigCMSM == Buy);
      bool trendSignalSell = (sigCFFP == Sell && sigCMSM == Sell);

      if (trendSignalBuy && sigRSI == Buy) return Buy;
      if (trendSignalSell && sigRSI == Sell) return Sell;
      return None;
   }
   if (source == SIGNAL_SRC_CFFP_CMSM_AND_CSI) // (CFFP & CMSM) & CSI
   {
      bool trendSignalBuy = (sigCFFP == Buy && sigCMSM == Buy);
      bool trendSignalSell = (sigCFFP == Sell && sigCMSM == Sell);

      if (trendSignalBuy && sigCSI == Buy) return Buy;
      if (trendSignalSell && sigCSI == Sell) return Sell;
      return None;
   }

   // Default if source is not recognized or combination logic isn't met
   PrintFormat("ORTBO Warning: Unhandled signal source (%s) in GetSignal or no combination logic matched.", EnumToString(source));
   return None;
}

//+------------------------------------------------------------------+
//| Process Grid Logic (Corrected, Minimal Logging)                  |
//+------------------------------------------------------------------+
void ProcessGridLogic(Grid* gridInstance, Direction sigMA, Direction sigRSI, Direction sigCSI, Direction sigCFFP, Direction sigCMSM) // Takes signals as args
{
   // Ensure grid pointer is valid
   if(CheckPointer(gridInstance) == POINTER_INVALID)
   {
      Print("[ERROR] ORTBO: ProcessGridLogic called with invalid gridInstance pointer.");
      return;
   }

   // --- Get grid configuration and current state ---
   ulong gridMagic         = gridInstance.magic;         // Direct access
   string gridSymbol       = gridInstance.symbol.Name();
   string gridName         = gridInstance.name;
   int currentGridCount    = gridInstance.GetCount();
   Direction currentGridDir= gridInstance.GetDirection();
   ENUM_SIGNAL_SOURCE triggerSrc = gridInstance.GetTriggerSource();
   ENUM_SIGNAL_SOURCE strat1Src  = gridInstance.GetStrat1Source();
   ENUM_SIGNAL_SOURCE strat2Src  = gridInstance.GetStrat2Source();
   ENUM_GRID_DIRECTION allowedDir = gridInstance.GetAllowedDirection();

   // --- Get actual signal values based on configuration ---
   int symbolIdx = SymbolIndexBySymbol(gridSymbol);
   if(symbolIdx < 0)
   {
        // Keep this error log as it prevents any action
        PrintFormat("[ERROR] ORTBO PGL[%s %lu %s]: Cannot find symbol index for %s. Aborting logic.",
                    gridName, gridMagic, gridSymbol, gridSymbol);
        return;
   }
   Direction triggerSignal = GetSignal(symbolIdx, triggerSrc);
   Direction strat1Signal  = GetSignal(symbolIdx, strat1Src);
   Direction strat2Signal  = GetSignal(symbolIdx, strat2Src);

   // --- Grid Starting Logic ---
   if (currentGridCount == 0)
   {
      // Check for existing positions
      int existingPositions = 0;
      CPositionInfo positionInfo;
      for(int k = PositionsTotal() - 1; k >= 0; k--)
      {
         if(positionInfo.SelectByIndex(k))
         {
            if (positionInfo.Magic() == gridInstance.magic &&
                positionInfo.Symbol() == gridSymbol)
            {
               existingPositions++;
               // --- LOG: Informative log when preventing action ---
               PrintFormat("ORTBO PGL[%s %lu %s]: Found existing position #%lu. Grid start prevented.",
                           gridName, gridMagic, gridSymbol, positionInfo.Ticket());
               break; // Found one, no need to check further
            }
         }
         // Removed warning log for failed selection for minimal logging
      }

      // Only proceed if NO existing positions were found
      if(existingPositions == 0)
      {
         // Check trigger signal
         if(triggerSignal == Buy || triggerSignal == Sell)
         {
            // Check allowed direction
            bool directionAllowed = false;
            if (allowedDir == GRID_DIR_BOTH) { directionAllowed = true; }
            else if (allowedDir == GRID_DIR_BUY_ONLY && triggerSignal == Buy) { directionAllowed = true; }
            else if (allowedDir == GRID_DIR_SELL_ONLY && triggerSignal == Sell) { directionAllowed = true; }

            // If conditions met, attempt to start
            if(directionAllowed)
            {
               // --- LOG: Action about to be taken ---
               PrintFormat("ORTBO PGL[%s %lu %s]: Starting grid. Trigger: %s (%s), Direction: %s",
                           gridName, gridMagic, gridSymbol,
                           EnumToString(triggerSrc), EnumToString(triggerSignal), EnumToString(triggerSignal));
               g_gridEngine.StartGrid(gridInstance, triggerSignal);
            }
            // Removed log for 'direction not allowed'
         }
         // Removed log for 'trigger signal is None'
      }
      // Removed log for 'existing positions > 0'
   }
   // --- Grid Extending Logic ---
   else // currentGridCount > 0
   {
      // Check grid direction validity
      if(currentGridDir == None )
      {
         // Keep this warning as it indicates a potential state issue preventing action
         PrintFormat("[WARN] ORTBO PGL[%s %lu %s]: Grid count is %d but direction is %s. Skipping extension check.",
                      gridName, gridMagic, gridSymbol, currentGridCount, EnumToString(currentGridDir));
         return;
      }

      // 1. Check distance
      bool distanceMet = gridInstance.CheckDistance();

      if(distanceMet) // Only proceed if distance is met
      {
         // 2. Check allowed direction
         bool directionAllowed = false;
         if (allowedDir == GRID_DIR_BOTH) { directionAllowed = true; }
         else if (allowedDir == GRID_DIR_BUY_ONLY && currentGridDir == Buy) { directionAllowed = true; }
         else if (allowedDir == GRID_DIR_SELL_ONLY && currentGridDir == Sell) { directionAllowed = true; }

         // 3. Check strategy signals
         bool signalAllowsExtension = false;
         string allowingSignal = ""; // Initialize empty
         if (strat1Signal == currentGridDir) { signalAllowsExtension = true; allowingSignal = EnumToString(strat1Src); }
         if (strat2Signal == currentGridDir)
         {
              if (signalAllowsExtension) allowingSignal += "+" + EnumToString(strat2Src);
              else { signalAllowsExtension = true; allowingSignal = EnumToString(strat2Src); }
         }

         // If all conditions met, attempt to extend
         // The CheckDistance call above set gridInstance.nextGridLevelHit if true
         if (gridInstance.nextGridLevelHit && directionAllowed && signalAllowsExtension)
         {
              // --- LOG: Action about to be taken ---
              PrintFormat("ORTBO PGL[%s %lu %s]: Extending grid (Level %d). Direction: %s, Signal: %s",
                          gridName, gridMagic, gridSymbol, currentGridCount + 1,
                          EnumToString(currentGridDir), allowingSignal);
              g_gridEngine.ExtendGrid(gridInstance);
         }
         // Removed log for 'conditions not met for extension'
      }
      // Removed log for 'distance not met'
   } // End of Grid Extending Logic
}

//+------------------------------------------------------------------+
//| SymbolIndexBySymbol                                              |
//+------------------------------------------------------------------+
int SymbolIndexBySymbol(const string symbol)
{
   for(int i = 0; i < NumberOfTradeableSymbols; i++)
   {
      if(SymbolArray[i] == symbol)
      {
         return i;
      }
   }
   return -1;
}

//+------------------------------------------------------------------+