//+------------------------------------------------------------------+
//|                                   CChartObjectConfigLoader.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"
#property version   "1.00"

#ifndef ORTBO_CCHART_OBJECT_CONFIG_LOADER_MQH
#define ORTBO_CCHART_OBJECT_CONFIG_LOADER_MQH

#include <Object.mqh>
#include <ChartObjects/ChartObjectsTxtControls.mqh>
#include "../Base/CConfigBase.mqh"
#include "../CConfigFactory.mqh"
#include "../../Enums/ORTBO_Enums.mqh"

//+------------------------------------------------------------------+
//| Configuration loader from chart objects                         |
//| Stores and retrieves configurations using chart text objects   |
//+------------------------------------------------------------------+
class CChartObjectConfigLoader : public CObject
  {
private:
   long              m_chartId;        // Chart ID to work with
   string            m_objectPrefix;   // Prefix for configuration objects
   
public:
   //--- Constructor
                     CChartObjectConfigLoader(long chartId = 0, const string objectPrefix = "ORTBO_Config_")
     {
      m_chartId = (chartId == 0) ? ChartID() : chartId;
      m_objectPrefix = objectPrefix;
     }
   
   //--- Load configuration from chart object
   CConfigBase*      LoadFromChartObject(ENUM_SIGNAL_SOURCE source, const string symbol = "")
     {
      string objectName = GetObjectName(source, symbol);
      
      if(!ObjectFind(m_chartId, objectName))
        {
         PrintFormat("CChartObjectConfigLoader: Chart object '%s' not found", objectName);
         return NULL;
        }
      
      string configString = ObjectGetString(m_chartId, objectName, OBJPROP_TEXT);
      if(configString == "")
        {
         PrintFormat("CChartObjectConfigLoader: Empty configuration in chart object '%s'", objectName);
         return NULL;
        }
      
      return CConfigFactory::CreateFromString(source, configString);
     }
   
   //--- Save configuration to chart object
   bool              SaveToChartObject(CConfigBase* config, ENUM_SIGNAL_SOURCE source, const string symbol = "")
     {
      if(config == NULL) return false;
      
      string objectName = GetObjectName(source, symbol);
      string configString = config.SaveToString();
      
      // Remove existing object if it exists
      ObjectDelete(m_chartId, objectName);
      
      // Create new text object
      if(!ObjectCreate(m_chartId, objectName, OBJ_LABEL, 0, 0, 0))
        {
         PrintFormat("CChartObjectConfigLoader: Failed to create chart object '%s'", objectName);
         return false;
        }
      
      // Set object properties
      ObjectSetString(m_chartId, objectName, OBJPROP_TEXT, configString);
      ObjectSetInteger(m_chartId, objectName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
      ObjectSetInteger(m_chartId, objectName, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(m_chartId, objectName, OBJPROP_YDISTANCE, 10 + GetObjectYOffset(source));
      ObjectSetInteger(m_chartId, objectName, OBJPROP_FONTSIZE, 8);
      ObjectSetString(m_chartId, objectName, OBJPROP_FONT, "Courier New");
      ObjectSetInteger(m_chartId, objectName, OBJPROP_COLOR, clrDarkGray);
      ObjectSetInteger(m_chartId, objectName, OBJPROP_HIDDEN, true); // Hide from object list
      ObjectSetInteger(m_chartId, objectName, OBJPROP_SELECTABLE, false);
      
      // Add description
      string description = StringFormat("ORTBO Config: %s %s", EnumToString(source), symbol);
      ObjectSetString(m_chartId, objectName, OBJPROP_TOOLTIP, description);
      
      return true;
     }
   
   //--- Load all configurations from chart objects for a symbol
   bool              LoadAllFromChartObjects(const string symbol,
                                             CConfigBase* &configs[],
                                             ENUM_SIGNAL_SOURCE &sources[],
                                             int &count)
     {
      count = 0;
      
      ENUM_SIGNAL_SOURCE allSources[] = {
         SIGNAL_SRC_MA,
         SIGNAL_SRC_RSI,
         SIGNAL_SRC_CSI,
         SIGNAL_SRC_CFFP,
         SIGNAL_SRC_CMSM
      };
      
      int maxSources = ArraySize(allSources);
      ArrayResize(configs, maxSources);
      ArrayResize(sources, maxSources);
      
      for(int i = 0; i < maxSources; i++)
        {
         CConfigBase* config = LoadFromChartObject(allSources[i], symbol);
         if(config != NULL)
           {
            configs[count] = config;
            sources[count] = allSources[i];
            count++;
           }
        }
      
      return count > 0;
     }
   
   //--- Save all configurations to chart objects for a symbol
   bool              SaveAllToChartObjects(const string symbol,
                                           CConfigBase* &configs[],
                                           ENUM_SIGNAL_SOURCE &sources[],
                                           int count)
     {
      bool allSuccess = true;
      
      for(int i = 0; i < count; i++)
        {
         if(!SaveToChartObject(configs[i], sources[i], symbol))
           {
            allSuccess = false;
            PrintFormat("CChartObjectConfigLoader: Failed to save config for %s %s", 
                       EnumToString(sources[i]), symbol);
           }
        }
      
      return allSuccess;
     }
   
   //--- Check if configuration exists in chart object
   bool              ConfigExists(ENUM_SIGNAL_SOURCE source, const string symbol = "")
     {
      string objectName = GetObjectName(source, symbol);
      return (ObjectFind(m_chartId, objectName) >= 0);
     }
   
   //--- Delete configuration chart object
   bool              DeleteConfig(ENUM_SIGNAL_SOURCE source, const string symbol = "")
     {
      string objectName = GetObjectName(source, symbol);
      return ObjectDelete(m_chartId, objectName);
     }
   
   //--- Delete all configuration chart objects for a symbol
   void              DeleteAllConfigs(const string symbol = "")
     {
      ENUM_SIGNAL_SOURCE allSources[] = {
         SIGNAL_SRC_MA,
         SIGNAL_SRC_RSI,
         SIGNAL_SRC_CSI,
         SIGNAL_SRC_CFFP,
         SIGNAL_SRC_CMSM
      };
      
      for(int i = 0; i < ArraySize(allSources); i++)
        {
         DeleteConfig(allSources[i], symbol);
        }
     }
   
   //--- Get list of symbols with configurations
   void              GetConfiguredSymbols(string &symbols[])
     {
      ArrayResize(symbols, 0);
      
      int totalObjects = ObjectsTotal(m_chartId);
      for(int i = 0; i < totalObjects; i++)
        {
         string objectName = ObjectName(m_chartId, i);
         if(StringFind(objectName, m_objectPrefix) == 0)
           {
            string symbol = ExtractSymbolFromObjectName(objectName);
            if(symbol != "" && !IsSymbolInArray(symbol, symbols))
              {
               int size = ArraySize(symbols);
               ArrayResize(symbols, size + 1);
               symbols[size] = symbol;
              }
           }
        }
     }
   
   //--- Create configuration summary for display
   string            CreateConfigSummary(const string symbol = "")
     {
      string summary = "Chart Object Configurations";
      if(symbol != "") summary += " for " + symbol;
      summary += ":\n";
      
      CConfigBase* configs[];
      ENUM_SIGNAL_SOURCE sources[];
      int count;

      if(LoadAllFromChartObjects(symbol, configs, sources, count))
        {
         for(int i = 0; i < count; i++)
           {
            summary += StringFormat("%s: %s\n", EnumToString(sources[i]), 
                                   configs[i].SaveToString());
            delete configs[i]; // Clean up
           }
        }
      else
        {
         summary += "No configurations found\n";
        }
      
      return summary;
     }
   
   //--- Getters/Setters
   long              GetChartId() const { return m_chartId; }
   void              SetChartId(long chartId) { m_chartId = chartId; }
   string            GetObjectPrefix() const { return m_objectPrefix; }
   void              SetObjectPrefix(const string prefix) { m_objectPrefix = prefix; }

private:
   //--- Generate object name for configuration
   string            GetObjectName(ENUM_SIGNAL_SOURCE source, const string symbol)
     {
      string name = m_objectPrefix + EnumToString(source);
      if(symbol != "") name += "_" + symbol;
      return name;
     }
   
   //--- Get Y offset for object positioning
   int               GetObjectYOffset(ENUM_SIGNAL_SOURCE source)
     {
      switch(source)
        {
         case SIGNAL_SRC_MA:    return 0;
         case SIGNAL_SRC_RSI:   return 20;
         case SIGNAL_SRC_CSI:   return 40;
         case SIGNAL_SRC_CFFP:  return 60;
         case SIGNAL_SRC_CMSM:  return 80;
         default:               return 100;
        }
     }
   
   //--- Extract symbol from object name
   string            ExtractSymbolFromObjectName(const string objectName)
     {
      string parts[];
      int count = StringSplit(objectName, '_', parts);
      
      if(count >= 3) // Prefix_Source_Symbol
        {
         return parts[count - 1]; // Last part is symbol
        }
      
      return ""; // No symbol specified
     }
   
   //--- Check if symbol is already in array
   bool              IsSymbolInArray(const string symbol, const string &symbols[])
     {
      for(int i = 0; i < ArraySize(symbols); i++)
        {
         if(symbols[i] == symbol)
            return true;
        }
      return false;
     }
  };

#endif // ORTBO_CCHART_OBJECT_CONFIG_LOADER_MQH
