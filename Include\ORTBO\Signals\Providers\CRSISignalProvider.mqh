//+------------------------------------------------------------------+
//|                                       CRSISignalProvider.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"

#ifndef ORTBO_RSI_SIGNAL_PROVIDER_MQH
#define ORTBO_RSI_SIGNAL_PROVIDER_MQH

#include "../Base/CSignalProviderBase.mqh"
#include "../ProviderTypeDefinitions.mqh"  // Central provider type definitions
// ORTBO_Enums.mqh is included via CSignalProviderBase -> ISignalProvider -> SignalR<PERSON>ult

//+------------------------------------------------------------------+
//| Concrete Relative Strength Index (RSI) Signal Provider.          |
//+------------------------------------------------------------------+
class CRSISignalProvider : public CSignalProviderBase
  {
private:
   // RSI specific parameters
   int               m_rsiPeriod;
   double            m_rsiMinimum;         // Buy threshold
   double            m_rsiMaximum;         // Sell threshold
   bool              m_rsiFilterInverter;
   // Note: m_timeframe for RSI calculation is inherited from CSignalProviderBase

public:
   //--- Constructor
                     CRSISignalProvider(
                                     const int period,
                                     const double minimum_level,
                                     const double maximum_level,
                                     const bool filter_inverter,
                                     const ENUM_TIMEFRAMES signal_timeframe // Timeframe for RSI calculation
                                     )
                       : CSignalProviderBase("RSI") // Call base constructor with provider name
                       {
                        // Store RSI specific parameters
                        m_rsiPeriod = period;
                        m_rsiMinimum = minimum_level;
                        m_rsiMaximum = maximum_level;
                        m_rsiFilterInverter = filter_inverter;
                        
                        // Set the primary timeframe for this provider's RSI calculations
                        m_timeframe = signal_timeframe; 
                       }

   //--- Destructor (virtual from ISignalProvider, base class handles m_indicatorHandle release)
   virtual           ~CRSISignalProvider()
                       {
                        // CRSISignalProvider specific cleanup, if any.
                       }

   //--- Initialization
   virtual bool      Initialize(const string forSymbol/*, const int symbolIndex, CParameters* params = NULL*/) override
                       {
                        if(!CSignalProviderBase::Initialize(forSymbol)) // Call base
                           return false;
                           
                        m_lastSignalResult.calculatedOnTimeframe = m_timeframe;

                        // Create the iRSI indicator handle
                        // m_indicatorHandle is inherited from CSignalProviderBase
                        m_indicatorHandle = iRSI(m_symbol, m_timeframe, m_rsiPeriod, PRICE_CLOSE); // RSI typically uses PRICE_CLOSE

                        if(m_indicatorHandle == INVALID_HANDLE)
                          {
                           SetErrorResult(StringFormat("Failed to create iRSI handle for %s on %s. Period:%d. Error: %d",
                                                       m_symbol, EnumToString(m_timeframe), m_rsiPeriod, GetLastError()));
                           return false;
                          }

                        // Resize internal buffer (m_indicatorBuffers from base class) for RSI values
                        if(ArrayResize(m_indicatorBuffers, 1) != 1)
                          {
                           SetErrorResult(StringFormat("Failed to resize m_indicatorBuffers for RSI provider on %s.", m_symbol));
                           IndicatorRelease(m_indicatorHandle);
                           m_indicatorHandle = INVALID_HANDLE;
                           return false;
                          }
                        ArraySetAsSeries(m_indicatorBuffers, true);

                        //PrintFormat("%s: Initialized for %s on %s.", GetProviderName(), m_symbol, EnumToString(m_timeframe));
                        return true;
                       }
                       
   //--- Calculate and return the RSI signal
   virtual SignalResult GetSignal() override
                       {
                        // 1. Basic Checks
                        if(!m_enabled)
                          {
                           SetErrorResult("Provider is disabled.");
                           return m_lastSignalResult;
                          }
                        if(m_indicatorHandle == INVALID_HANDLE)
                          {
                           SetErrorResult("RSI Indicator handle is invalid.");
                           return m_lastSignalResult;
                          }
                        if(m_symbol == "")
                          {
                           SetErrorResult("Symbol not set for RSI provider.");
                           return m_lastSignalResult;
                          }

                        // 2. "Once per bar" logic for the indicator's timeframe (m_timeframe)
                        if (!IsNewCalculationNeededForBar())
                          {
                           return m_lastSignalResult; 
                          }
                        
                        m_lastSignalResult.isValid = false; 
                        m_lastSignalResult.direction = None;
                        m_lastSignalResult.strength = 0.0;
                        m_lastSignalResult.errorMessage = "";
                        m_lastSignalResult.primaryIndicatorValue = EMPTY_VALUE;
                        m_lastSignalResult.supportingPriceValue = EMPTY_VALUE;

                        // 3. --- Actual RSI Signal Calculation ---
                        // Get RSI value of the previous completed bar on m_timeframe
                        if(!CopyIndicatorBufferSafe(0, 1, 1)) // Uses m_indicatorBuffers
                          {
                           return m_lastSignalResult; // Error set by CopyIndicatorBufferSafe
                          }
                        double rsiValue = m_indicatorBuffers[0];
                        
                        // Get close prices for confirmation from the *chart's current period*
                        // This matches the logic in your original GetSignalRSI
                        double prevChartClose = iClose(m_symbol, PERIOD_CURRENT, 2);
                        double currChartClose = iClose(m_symbol, PERIOD_CURRENT, 1);

                        if((prevChartClose == 0.0 || currChartClose == 0.0) && TerminalInfoInteger(TERMINAL_CONNECTED))
                          {
                           SetErrorResult(StringFormat("Failed to get chart's current/previous close prices for %s.", m_symbol));
                           return m_lastSignalResult;
                          }

                        Direction signal = None;
                        // Original logic from your GetSignalRSI:
                        // if (prevClose > currClose && m_RSIBuffer[0] < InpRsiMinimum) signal = Buy;
                        // if (prevClose < currClose && m_RSIBuffer[0] > InpRsiMaximum) signal = Sell;
                        if (prevChartClose > currChartClose && rsiValue < m_rsiMinimum)
                           signal = Buy;
                        if (prevChartClose < currChartClose && rsiValue > m_rsiMaximum) // Can overwrite Buy if both true
                           signal = Sell;

                        // Apply filter inverter
                        if(m_rsiFilterInverter)
                          {
                           if(signal == Buy) signal = Sell;
                           else if(signal == Sell) signal = Buy;
                          }
                        
                        // 4. Populate and return the result
                        // Strength calculation can be more sophisticated later
                        double strength = (signal != None) ? 100.0 : 0.0; 
                        if(signal == Buy && rsiValue < m_rsiMinimum) strength = MathMin(100, 100 * (m_rsiMinimum - rsiValue) / m_rsiMinimum); // Example strength
                        if(signal == Sell && rsiValue > m_rsiMaximum) strength = MathMin(100, 100 * (rsiValue - m_rsiMaximum) / (100 - m_rsiMaximum)); // Example strength

                        SetSuccessResult(signal, strength, rsiValue, currChartClose);
                        
                        UpdateLastSuccessfulCalculationTime(); 
                           
                        return m_lastSignalResult;
                       }

   //--- Provider Type
   virtual int       Type() const override { return MQL5_PROVIDER_TYPE_ID_RSI; }
  };

#endif // ORTBO_RSI_SIGNAL_PROVIDER_MQH
