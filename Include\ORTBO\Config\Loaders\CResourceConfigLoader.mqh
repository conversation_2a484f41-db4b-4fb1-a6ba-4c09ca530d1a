//+------------------------------------------------------------------+
//|                                       CResourceConfigLoader.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"
#property version   "1.00"

#ifndef ORTBO_CRESOURCE_CONFIG_LOADER_MQH
#define ORTBO_CRESOURCE_CONFIG_LOADER_MQH

#include <Object.mqh>
#include "../Base/CConfigBase.mqh"
#include "../CConfigFactory.mqh"
#include "../../Enums/ORTBO_Enums.mqh"

//+------------------------------------------------------------------+
//| Configuration loader from embedded resources                    |
//| Loads configuration templates embedded as resources             |
//+------------------------------------------------------------------+
class CResourceConfigLoader : public CObject
  {
private:
   //--- Available configuration templates
   static string s_availableTemplates[];
   
   //--- Initialize available templates list
   static void InitializeTemplateList()
     {
      ArrayResize(s_availableTemplates, 3);
      s_availableTemplates[0] = "Conservative";
      s_availableTemplates[1] = "Aggressive";
      s_availableTemplates[2] = "Balanced";
     }

public:
   //--- Load configuration template from resources
   static CConfigBase* LoadTemplate(const string templateName, ENUM_SIGNAL_SOURCE source)
     {
      if(!IsTemplateAvailable(templateName))
        {
         PrintFormat("CResourceConfigLoader: Template '%s' not available", templateName);
         return NULL;
        }
      
      string resourcePath = "::Config\\Templates\\" + templateName + ".cfg";
      string configString = "";
      
      // Try to read from resource
      if(!ReadResourceString(resourcePath, configString))
        {
         PrintFormat("CResourceConfigLoader: Failed to read resource '%s'", resourcePath);
         return NULL;
        }
      
      // Parse the configuration string to find the specific provider config
      string providerConfig = ExtractProviderConfig(configString, source);
      if(providerConfig == "")
        {
         PrintFormat("CResourceConfigLoader: No configuration found for %s in template %s", 
                    EnumToString(source), templateName);
         return NULL;
        }
      
      return CConfigFactory::CreateFromString(source, providerConfig);
     }
   
   //--- Load all configurations from a template
   static bool LoadAllFromTemplate(const string templateName, 
                                   CConfigBase* configs[], 
                                   ENUM_SIGNAL_SOURCE sources[], 
                                   int &count)
     {
      count = 0;
      
      if(!IsTemplateAvailable(templateName))
        {
         PrintFormat("CResourceConfigLoader: Template '%s' not available", templateName);
         return false;
        }
      
      string resourcePath = "::Config\\Templates\\" + templateName + ".cfg";
      string configString = "";
      
      if(!ReadResourceString(resourcePath, configString))
        {
         PrintFormat("CResourceConfigLoader: Failed to read resource '%s'", resourcePath);
         return false;
        }
      
      // Parse all provider configurations from the template
      ENUM_SIGNAL_SOURCE allSources[] = {
         SIGNAL_SRC_MA,
         SIGNAL_SRC_RSI,
         SIGNAL_SRC_CSI,
         SIGNAL_SRC_CFFP,
         SIGNAL_SRC_CMSM
      };
      
      int maxSources = ArraySize(allSources);
      ArrayResize(configs, maxSources);
      ArrayResize(sources, maxSources);
      
      for(int i = 0; i < maxSources; i++)
        {
         string providerConfig = ExtractProviderConfig(configString, allSources[i]);
         if(providerConfig != "")
           {
            CConfigBase* config = CConfigFactory::CreateFromString(allSources[i], providerConfig);
            if(config != NULL)
              {
               configs[count] = config;
               sources[count] = allSources[i];
               count++;
              }
           }
        }
      
      return count > 0;
     }
   
   //--- Check if template is available
   static bool IsTemplateAvailable(const string templateName)
     {
      if(ArraySize(s_availableTemplates) == 0)
         InitializeTemplateList();
      
      for(int i = 0; i < ArraySize(s_availableTemplates); i++)
        {
         if(s_availableTemplates[i] == templateName)
            return true;
        }
      return false;
     }
   
   //--- Get list of available templates
   static void GetAvailableTemplates(string &templates[])
     {
      if(ArraySize(s_availableTemplates) == 0)
         InitializeTemplateList();
      
      ArrayResize(templates, ArraySize(s_availableTemplates));
      for(int i = 0; i < ArraySize(s_availableTemplates); i++)
        {
         templates[i] = s_availableTemplates[i];
        }
     }
   
   //--- Create configuration summary from template
   static string CreateTemplateSummary(const string templateName)
     {
      string summary = "Template '" + templateName + "' Configuration Summary:\n";
      
      CConfigBase* configs[];
      ENUM_SIGNAL_SOURCE sources[];
      int count;
      
      if(LoadAllFromTemplate(templateName, configs, sources, count))
        {
         for(int i = 0; i < count; i++)
           {
            summary += StringFormat("%s: %s\n", EnumToString(sources[i]), 
                                   configs[i].SaveToString());
            delete configs[i]; // Clean up
           }
        }
      else
        {
         summary += "Failed to load template configurations\n";
        }
      
      return summary;
     }

private:
   //--- Read string from resource (placeholder - MQL5 specific implementation needed)
   static bool ReadResourceString(const string resourcePath, string &content)
     {
      // In MQL5, this would use ResourceReadString() or similar
      // For now, we'll simulate with default configurations
      content = GenerateDefaultTemplateContent(resourcePath);
      return (content != "");
     }
   
   //--- Extract provider-specific configuration from template string
   static string ExtractProviderConfig(const string templateContent, ENUM_SIGNAL_SOURCE source)
     {
      string sectionName = "[" + EnumToString(source) + "]";
      string lines[];
      int lineCount = StringSplit(templateContent, '\n', lines);
      
      bool inSection = false;
      string config = "";
      
      for(int i = 0; i < lineCount; i++)
        {
         string line = StringTrimLeft(StringTrimRight(lines[i]));
         
         if(StringFind(line, "[") == 0) // New section
           {
            if(inSection) break; // End of our section
            inSection = (line == sectionName);
           }
         else if(inSection && line != "" && StringFind(line, "#") != 0) // Not comment
           {
            if(config != "") config += "|";
            config += line;
           }
        }
      
      return config;
     }
   
   //--- Generate default template content (fallback)
   static string GenerateDefaultTemplateContent(const string resourcePath)
     {
      if(StringFind(resourcePath, "Conservative") >= 0)
        {
         return GenerateConservativeTemplate();
        }
      else if(StringFind(resourcePath, "Aggressive") >= 0)
        {
         return GenerateAggressiveTemplate();
        }
      else if(StringFind(resourcePath, "Balanced") >= 0)
        {
         return GenerateBalancedTemplate();
        }
      
      return "";
     }
   
   //--- Generate conservative template
   static string GenerateConservativeTemplate()
     {
      return "# Conservative Trading Configuration\n"
             "[SIGNAL_SRC_MA]\n"
             "1|60|0|0|21|0|0|0|10\n"
             "[SIGNAL_SRC_RSI]\n"
             "1|240|0|21|25.0|75.0\n"
             "[SIGNAL_SRC_CSI]\n"
             "1|60|0|30|2\n"
             "[SIGNAL_SRC_CFFP]\n"
             "1|60|0|8|13|0|0\n"
             "[SIGNAL_SRC_CMSM]\n"
             "1|240|0|1.5|0\n";
     }
   
   //--- Generate aggressive template
   static string GenerateAggressiveTemplate()
     {
      return "# Aggressive Trading Configuration\n"
             "[SIGNAL_SRC_MA]\n"
             "1|5|0|0|8|0|0|0|3\n"
             "[SIGNAL_SRC_RSI]\n"
             "1|15|0|10|35.0|65.0\n"
             "[SIGNAL_SRC_CSI]\n"
             "1|15|0|10|1\n"
             "[SIGNAL_SRC_CFFP]\n"
             "1|15|0|3|8|0|0\n"
             "[SIGNAL_SRC_CMSM]\n"
             "1|60|0|3.0|0\n";
     }
   
   //--- Generate balanced template
   static string GenerateBalancedTemplate()
     {
      return "# Balanced Trading Configuration\n"
             "[SIGNAL_SRC_MA]\n"
             "1|15|0|0|14|1|0|0|6\n"
             "[SIGNAL_SRC_RSI]\n"
             "1|60|0|14|30.0|70.0\n"
             "[SIGNAL_SRC_CSI]\n"
             "1|60|0|20|1\n"
             "[SIGNAL_SRC_CFFP]\n"
             "1|60|0|5|13|0|0\n"
             "[SIGNAL_SRC_CMSM]\n"
             "1|60|0|2.0|0\n";
     }
  };

// Static member definition
string CResourceConfigLoader::s_availableTemplates[];

#endif // ORTBO_CRESOURCE_CONFIG_LOADER_MQH
