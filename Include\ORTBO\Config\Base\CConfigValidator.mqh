//+------------------------------------------------------------------+
//|                                            CConfigValidator.mqh |
//|                                      Copyright 2025, Your Name   |
//|                                     https://www.yourwebsite.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name"
#property link      "https://www.yourwebsite.com"
#property version   "1.00"

#ifndef ORTBO_CCONFIG_VALIDATOR_MQH
#define ORTBO_CCONFIG_VALIDATOR_MQH

#include <Object.mqh>

//+------------------------------------------------------------------+
//| Configuration validation helper class                            |
//+------------------------------------------------------------------+
class CConfigValidator : public CObject
  {
public:
   //--- Validation methods for common parameter types
   static bool       ValidateIntRange(const int value, const int minValue, const int maxValue, string &error)
     {
      if(value < minValue || value > maxValue)
        {
         error = StringFormat("Value %d is outside valid range [%d, %d]", value, minValue, maxValue);
         return false;
        }
      return true;
     }
   
   static bool       ValidateDoubleRange(const double value, const double minValue, const double maxValue, string &error)
     {
      if(value < minValue || value > maxValue)
        {
         error = StringFormat("Value %.5f is outside valid range [%.5f, %.5f]", value, minValue, maxValue);
         return false;
        }
      return true;
     }
   
   static bool       ValidateTimeframe(const ENUM_TIMEFRAMES timeframe, string &error)
     {
      // Check if timeframe is valid
      if(timeframe < PERIOD_M1 || timeframe > PERIOD_MN1)
        {
         if(timeframe != PERIOD_CURRENT)
           {
            error = "Invalid timeframe value";
            return false;
           }
        }
      return true;
     }
   
   static bool       ValidateMAMethod(const ENUM_MA_METHOD method, string &error)
     {
      if(method < MODE_SMA || method > MODE_LWMA)
        {
         error = "Invalid MA method";
         return false;
        }
      return true;
     }
   
   static bool       ValidateAppliedPrice(const ENUM_APPLIED_PRICE price, string &error)
     {
      if(price < PRICE_CLOSE || price > PRICE_WEIGHTED)
        {
         error = "Invalid applied price";
         return false;
        }
      return true;
     }
   
   static bool       ValidateSymbol(const string symbol, string &error)
     {
      if(symbol == "" || symbol == NULL)
        {
         error = "Symbol cannot be empty";
         return false;
        }
      
      // Check if symbol exists in Market Watch
      if(!SymbolSelect(symbol, true))
        {
         error = "Symbol '" + symbol + "' not found in Market Watch";
         return false;
        }
      
      return true;
     }
   
   //--- Validate string is not empty
   static bool       ValidateStringNotEmpty(const string value, const string fieldName, string &error)
     {
      if(value == "" || value == NULL)
        {
         error = fieldName + " cannot be empty";
         return false;
        }
      return true;
     }
   
   //--- Validate array size
   static bool       ValidateArraySize(const int size, const int minSize, const int maxSize, string &error)
     {
      if(size < minSize || size > maxSize)
        {
         error = StringFormat("Array size %d is outside valid range [%d, %d]", size, minSize, maxSize);
         return false;
        }
      return true;
     }
  };

#endif // ORTBO_CCONFIG_VALIDATOR_MQH