//+------------------------------------------------------------------+
//|                                          ISignalProvider.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"

#ifndef ORTBO_I_SIGNAL_PROVIDER_MQH
#define ORTBO_I_SIGNAL_PROVIDER_MQH

#include "SignalResult.mqh"       // For SignalResult return type
// ORTBO_Enums.mqh (containing Direction, ENUM_TIMEFRAMES) will be included via SignalResult.mqh

// Forward declaration for CParameters if used in Initialize, not used in Phase 1 directly
// class CParameters; 

//+------------------------------------------------------------------+
//| Interface for all signal providers.                              |
//| Note: MQL5 interfaces cannot inherit from classes.               |
//| If you need CObject functionality, implement it in the concrete  |
//| class that implements this interface.                            |
//+------------------------------------------------------------------+
interface ISignalProvider
  {
public:
//--- Initializes the signal provider for a specific symbol
//--- Parameters can be passed via a configuration object in later phases
   virtual bool      Initialize(const string forSymbol/*, const int symbolIndex, CParameters* params = NULL*/) = 0;

//--- Deinitializes the provider, releasing resources
   virtual void      Deinitialize() = 0;

//--- Calculates and returns the current signal.
//--- The provider instance knows its symbol and manages its timing.
   virtual SignalResult GetSignal() = 0;

//--- Gets the name of the provider
   virtual string    GetProviderName() const = 0;

//--- Gets a type identifier for the provider (for MQL5 "RTTI")
   virtual int       Type() const = 0;

//--- Checks if the provider is currently enabled
   virtual bool      IsEnabled() const = 0;

//--- Sets the enabled state of the provider
   virtual void      SetEnabled(bool enable) = 0;
   
//--- Gets the primary timeframe this provider calculates on
   virtual ENUM_TIMEFRAMES GetTimeframe() const = 0;
  };

#endif // ORTBO_I_SIGNAL_PROVIDER_MQH