# ORTBO Aggressive Trading Configuration Template
# This template focuses on quick signals and frequent trading
# Generated: 2025-01-27
# Version: 1.0

[SIGNAL_SRC_MA]
# Format: enabled|timeframe|filterInverter|period|method|appliedPrice|shift|marginPips
# Aggressive MA: Short period, low margin for quick signals
1|5|0|8|1|0|0|3

[SIGNAL_SRC_RSI]
# Format: enabled|timeframe|filterInverter|period|minimum|maximum
# Aggressive RSI: Short period, wider levels for more signals
1|15|0|10|35.0|65.0

[SIGNAL_SRC_CSI]
# Format: enabled|timeframe|filterInverter|maPeriod|maDelta
# Aggressive CSI: Short MA period for quick response
1|15|0|10|1

[SIGNAL_SRC_CFFP]
# Format: enabled|timeframe|filterInverter|fastMAPeriod|slowMAPeriod|maMethod|appliedPrice
# Aggressive CFFP: Close fast/slow MA for quick crossovers
1|15|0|3|8|0|0

[SIGNAL_SRC_CMSM]
# Format: enabled|timeframe|filterInverter|tradeLevel|timeIndex
# Aggressive CMSM: Higher trade level for more frequent signals
1|60|0|3.0|0

# Configuration Notes:
# - All timeframes set to lower values for quick response
# - RSI levels widened to 35/65 for more trading opportunities
# - MA periods decreased for faster signals
# - CFFP spread narrowed for quick trend detection
# - CMSM trade level increased for more signals
# - Margin pips reduced for MA to catch smaller moves
# - EMA method used for MA for faster response
