//+------------------------------------------------------------------+
//|                                     CCFFPSignalProvider.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"

#ifndef ORTBO_CFFP_SIGNAL_PROVIDER_MQH
#define ORTBO_CFFP_SIGNAL_PROVIDER_MQH

#include "../Base/CSignalProviderBase.mqh"
#include "../ProviderTypeDefinitions.mqh"  // Central provider type definitions
// ORTBO_Enums.mqh is included via CSignalProviderBase

//+------------------------------------------------------------------+
//| Concrete Currency Strength (CFFP) Signal Provider.               |
//| Manages a shared iCustom handle for the CCFp.ex5 indicator.      |
//+------------------------------------------------------------------+
class CCFFPSignalProvider : public CSignalProviderBase
  {
private:
   // --- Static members for shared indicator handle ---
   static int        s_sharedCFFPIndicatorHandle; // Shared handle for CCFp.ex5
   static int        s_sharedCFFPHandleUsers;     // Counter for users of the shared handle
   static string     s_cffpIndicatorPath;         // Path to the CCFp.ex5 indicator
   static bool       s_staticMembersInitialized;  // Flag to initialize static path once

   // --- Instance-specific members ---
   // CFFP parameters (used if this instance is the one creating the shared handle)
   int               m_cffpFastMAPeriod;
   int               m_cffpSlowMAPeriod;
   ENUM_MA_METHOD    m_cffpMAMethod;
   ENUM_APPLIED_PRICE m_cffpAppliedPrice;
   bool              m_cffpFilterInverter;
   // Note: m_timeframe for CFFP calculation is inherited from CSignalProviderBase

   // Currency mapping for this instance (for m_symbol)
   string            m_cffpCurrencyNames[8]; // Order from your original CFFP logic (CCFp.ex5 buffers 0-7)
   int               m_instanceBaseCcyIndex;  // Index in m_cffpCurrencyNames for m_symbol's base currency
   int               m_instanceQuoteCcyIndex; // Index in m_cffpCurrencyNames for m_symbol's quote currency
   
   double            m_allStrengthsBuffer[8]; // Buffer for all 8 currency strengths

   // --- Private static initializer for path ---
   static void       InitializeStaticMembers()
                       {
                        if(!s_staticMembersInitialized)
                          {
                           s_cffpIndicatorPath = "::CCFp.ex5"; // Using resource path
                           s_staticMembersInitialized = true;
                          }
                       }
public:
   //--- Constructor
                     CCFFPSignalProvider(
                                     const int fastMaPeriod,
                                     const int slowMaPeriod,
                                     const ENUM_MA_METHOD maMethod,
                                     const ENUM_APPLIED_PRICE appliedPrice,
                                     const bool filterInverter,
                                     const ENUM_TIMEFRAMES signalTimeframe
                                     )
                       : CSignalProviderBase("CFFP") // Call base constructor
                       {
                        InitializeStaticMembers();

                        m_cffpFastMAPeriod = fastMaPeriod;
                        m_cffpSlowMAPeriod = slowMaPeriod;
                        m_cffpMAMethod = maMethod;
                        m_cffpAppliedPrice = appliedPrice;
                        m_cffpFilterInverter = filterInverter;
                        m_timeframe = signalTimeframe; // Timeframe for the underlying CFFP indicator

                        // Initialize CFFP currency names array (matching your original EA's m_CFFPCurrencyNames)
                        m_cffpCurrencyNames[0] = "USD"; m_cffpCurrencyNames[1] = "EUR";
                        m_cffpCurrencyNames[2] = "GBP"; m_cffpCurrencyNames[3] = "CHF";
                        m_cffpCurrencyNames[4] = "JPY"; m_cffpCurrencyNames[5] = "AUD";
                        m_cffpCurrencyNames[6] = "CAD"; m_cffpCurrencyNames[7] = "NZD";
                        
                        m_instanceBaseCcyIndex = -1;
                        m_instanceQuoteCcyIndex = -1;
                        
                        ArrayInitialize(m_allStrengthsBuffer, EMPTY_VALUE);
                       }

   //--- Destructor
   virtual           ~CCFFPSignalProvider() {}

   //--- Initialization
   virtual bool      Initialize(const string forSymbol/*, const int symbolIndex, CParameters* params = NULL*/) override
                       {
                        if(!CSignalProviderBase::Initialize(forSymbol))
                           return false;
                           
                        m_lastSignalResult.calculatedOnTimeframe = m_timeframe;

                        if(!MapInstanceCurrencyIndices())
                           return false;

                        if(s_sharedCFFPIndicatorHandle == INVALID_HANDLE)
                          {
                           s_sharedCFFPIndicatorHandle = iCustom(_Symbol, m_timeframe, s_cffpIndicatorPath,
                                                                 m_cffpFastMAPeriod, m_cffpSlowMAPeriod,
                                                                 m_cffpMAMethod, m_cffpAppliedPrice);
                           
                           if(s_sharedCFFPIndicatorHandle == INVALID_HANDLE)
                             {
                              SetErrorResult(StringFormat("Failed to create shared CFFP handle on chart %s, TF %s. Error: %d",
                                                          _Symbol, EnumToString(m_timeframe), GetLastError()));
                              return false;
                             }
                          }
                        
                        s_sharedCFFPHandleUsers++;
                        m_indicatorHandle = s_sharedCFFPIndicatorHandle; // For base class checks
                        
                        // Resize to minimum size to avoid allocation issues
                        if(ArrayResize(m_indicatorBuffers, 1) != 1)
                          {
                           SetErrorResult(StringFormat("Failed to resize m_indicatorBuffers for CFFP provider on %s.", m_symbol));
                           return false;
                          }

                        return true;
                       }
                       
   //--- Deinitialization
   virtual void      Deinitialize() override
                       {
                        if(m_indicatorHandle != INVALID_HANDLE) // This instance was successfully initialized
                          {
                           s_sharedCFFPHandleUsers--;
                           if(s_sharedCFFPHandleUsers == 0 && s_sharedCFFPIndicatorHandle != INVALID_HANDLE)
                             {
                              IndicatorRelease(s_sharedCFFPIndicatorHandle);
                              s_sharedCFFPIndicatorHandle = INVALID_HANDLE;
                             }
                           m_indicatorHandle = INVALID_HANDLE; 
                          }
                        CSignalProviderBase::Deinitialize();
                       }

   //--- Calculate and return the CFFP signal for m_symbol
   virtual SignalResult GetSignal() override
                       {
                        if(!m_enabled) { SetErrorResult("Provider is disabled."); return m_lastSignalResult; }
                        if(s_sharedCFFPIndicatorHandle == INVALID_HANDLE) { SetErrorResult("Shared CFFP Indicator handle is invalid."); return m_lastSignalResult; }
                        if(m_symbol == "") { SetErrorResult("Symbol not set for CFFP provider instance."); return m_lastSignalResult; }
                        if(m_instanceBaseCcyIndex < 0 || m_instanceQuoteCcyIndex < 0) { SetErrorResult("CFFP currency indices not mapped."); return m_lastSignalResult; }

                        if (!IsNewCalculationNeededForBar()) { return m_lastSignalResult; }
                        
                        m_lastSignalResult.isValid = false; m_lastSignalResult.direction = None;
                        m_lastSignalResult.strength = 0.0; m_lastSignalResult.errorMessage = "";
                        m_lastSignalResult.primaryIndicatorValue = EMPTY_VALUE; m_lastSignalResult.supportingPriceValue = EMPTY_VALUE;

                        if(BarsCalculated(s_sharedCFFPIndicatorHandle) < 2)
                          { SetErrorResult("Shared CFFP indicator not ready."); return m_lastSignalResult; }

                        bool allCopied = true;
                        double tempSingleBuffer[];
                        ArrayResize(tempSingleBuffer, 1);
                        ArraySetAsSeries(tempSingleBuffer, true);
                        for(int i = 0; i < 8; i++)
                          {
                           ResetLastError();
                           int copied = CopyBuffer(s_sharedCFFPIndicatorHandle, i, 1, 1, tempSingleBuffer); // Shift 1 for last completed bar
                           if(copied <= 0)
                             {
                              SetErrorResult(StringFormat("Failed to copy CFFP buffer %d (%s). Err: %d", i, m_cffpCurrencyNames[i], GetLastError()));
                              allCopied = false; break;
                             }
                           m_allStrengthsBuffer[i] = tempSingleBuffer[0];
                          }
                        if(!allCopied) { return m_lastSignalResult; }

                        // --- Actual CFFP Signal Calculation for m_symbol ---
                        // Replicate your existing CFFP logic (both absolute value and strongest vs weakest)
                        Direction signal = None;
                        double baseStrength = m_allStrengthsBuffer[m_instanceBaseCcyIndex];
                        double quoteStrength = m_allStrengthsBuffer[m_instanceQuoteCcyIndex];
                        m_lastSignalResult.primaryIndicatorValue = baseStrength;   // Example
                        m_lastSignalResult.supportingPriceValue = quoteStrength; // Example

                        // Logic 1: Absolute Value (base > 0 && quote < 0)
                        Direction sig_abs = None;
                        if (baseStrength > 0 && quoteStrength < 0) sig_abs = Buy;
                        else if (baseStrength < 0 && quoteStrength > 0) sig_abs = Sell;

                        // Logic 2: Strongest vs Weakest Currency (from all 8 CFFP currencies)
                        Direction sig_sw = None;
                        if (sig_abs == None) // Only if absolute logic didn't yield a signal
                          {
                           double maxVal = -DBL_MAX; int strongestIdx = -1;
                           double minVal = DBL_MAX;  int weakestIdx = -1;
                           for(int j=0; j<8; j++)
                             {
                              if(m_allStrengthsBuffer[j] > maxVal) { maxVal = m_allStrengthsBuffer[j]; strongestIdx = j; }
                              if(m_allStrengthsBuffer[j] < minVal) { minVal = m_allStrengthsBuffer[j]; weakestIdx = j; }
                             }
                           
                           if (strongestIdx != -1 && weakestIdx != -1 && strongestIdx != weakestIdx)
                             {
                              if (m_instanceBaseCcyIndex == strongestIdx && m_instanceQuoteCcyIndex == weakestIdx) sig_sw = Buy;  // Base is strongest, Quote is weakest
                              else if (m_instanceBaseCcyIndex == weakestIdx && m_instanceQuoteCcyIndex == strongestIdx) sig_sw = Sell; // Base is weakest, Quote is strongest
                              // Your original logic was more nuanced:
                              // if (baseCFFPIdx == strongestCcyBufferIdx) sig_sw = Buy; else if (quoteCFFPIdx == weakestCcyBufferIdx) sig_sw = Buy; etc.
                              // Let's use the simpler Strongest Base / Weakest Quote for Buy and vice-versa for Sell
                             }
                          }
                        
                        // Combine Signals (Priority to Absolute Value Logic as per your EA)
                        if (sig_abs != None) signal = sig_abs;
                        else signal = sig_sw; // Use S/W if absolute is None (S/W might also be None)

                        if(m_cffpFilterInverter && signal != None)
                          { signal = (signal == Buy) ? Sell : Buy; }
                        
                        SetSuccessResult(signal, (signal != None ? 100.0 : 0.0), baseStrength, quoteStrength);
                        UpdateLastSuccessfulCalculationTime(); 
                           
                        return m_lastSignalResult;
                       }

   //--- Provider Type
   virtual int       Type() const override { return MQL5_PROVIDER_TYPE_ID_CFFP; }

private:
   //--- Maps the instance's m_symbol to base/quote currency indices from m_cffpCurrencyNames
   bool              MapInstanceCurrencyIndices()
                       {
                        string baseCcyName = SymbolInfoString(m_symbol, SYMBOL_CURRENCY_BASE);
                        string quoteCcyName = SymbolInfoString(m_symbol, SYMBOL_CURRENCY_PROFIT);

                        if(baseCcyName == "" || quoteCcyName == "")
                          { SetErrorResult("Failed to get Base/Quote currency for CFFP mapping on " + m_symbol); return false; }

                        m_instanceBaseCcyIndex = -1; m_instanceQuoteCcyIndex = -1;
                        for(int j=0; j<8; j++)
                          {
                           if(m_cffpCurrencyNames[j] == baseCcyName) m_instanceBaseCcyIndex = j;
                           if(m_cffpCurrencyNames[j] == quoteCcyName) m_instanceQuoteCcyIndex = j;
                          }

                        if(m_instanceBaseCcyIndex < 0 || m_instanceQuoteCcyIndex < 0)
                          { SetErrorResult("Cannot map CFFP currencies for " + m_symbol); return false; }
                        return true;
                       }
  };

// Initialize static members
int CCFFPSignalProvider::s_sharedCFFPIndicatorHandle = INVALID_HANDLE;
int CCFFPSignalProvider::s_sharedCFFPHandleUsers     = 0;
string CCFFPSignalProvider::s_cffpIndicatorPath      = "";
bool CCFFPSignalProvider::s_staticMembersInitialized = false;

#endif // ORTBO_CFFP_SIGNAL_PROVIDER_MQH
