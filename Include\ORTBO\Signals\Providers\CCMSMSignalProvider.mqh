//+------------------------------------------------------------------+
//|                                     CCMSMSignalProvider.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"

#ifndef ORTBO_CMSM_SIGNAL_PROVIDER_MQH
#define ORTBO_CMSM_SIGNAL_PROVIDER_MQH

#include "../Base/CSignalProviderBase.mqh"
#include "../ProviderTypeDefinitions.mqh"  // Central provider type definitions
// ORTBO_Enums.mqh is included via CSignalProviderBase

//+------------------------------------------------------------------+
//| Concrete Currency Momentum Strength Meter (CMSM) Signal Provider.|
//| Manages a shared iCustom handle for the CMSM indicator and reads |
//| chart labels for signals.                                        |
//+------------------------------------------------------------------+
class CCMSMSignalProvider : public CSignalProviderBase
  {
private:
   // --- Static members for shared indicator handle ---
   static int        s_sharedCMSMIndicatorHandle;
   static int        s_sharedCMSMHandleUsers;
   static string     s_cmsmIndicatorPath;        // Path to ###CMSMIndV17.06 (1).ex5
   static bool       s_staticMembersInitialized;

   // --- Instance-specific members ---
   // CMSM parameters (used if this instance creates the shared handle)
   double            m_cmsmTradeLevelParam;      // Corresponds to InpCMSM_TradeLevel
   ENUM_TIMEFRAMES   m_cmsmIndicatorTimeframe; // Corresponds to InpCMSMTimeFrame (used for iCustom and as m_timeframe)
   int               m_cmsmTimeIndexParam;       // Corresponds to InpCMSM_TimeIndex
   // IMPORTANT: If your ###CMSMIndV17.06 (1).ex5 takes more parameters in iCustom,
   // they must be added here, to the constructor, and to the iCustom call in Initialize().

   bool              m_cmsmFilterInverter;
   string            m_labelSearchPatternBase;   // Base pattern for label search, symbol will be appended

   // --- Private static initializer for path ---
   static void       InitializeStaticMembers()
                       {
                        if(!s_staticMembersInitialized)
                          {
                           // Path from your EA's #resource directive
                           s_cmsmIndicatorPath = "::Indicators\\###CMSMIndV17.06 (1).ex5"; 
                           s_staticMembersInitialized = true;
                          }
                       }
public:
   //--- Constructor
                     CCMSMSignalProvider(
                                     // Parameters for iCustom call to CMSM indicator
                                     const double tradeLevelParam,       // From InpCMSM_TradeLevel
                                     const ENUM_TIMEFRAMES indicatorTimeframe, // From InpCMSMTimeFrame
                                     const int timeIndexParam,          // From InpCMSM_TimeIndex
                                     // ... Add other CMSM iCustom parameters here if your indicator needs them ...
                                     const bool filterInverter          // From InpCMSMFilterInverter
                                     )
                       : CSignalProviderBase("CMSM") // Call base constructor
                       {
                        InitializeStaticMembers();

                        m_cmsmTradeLevelParam = tradeLevelParam;
                        m_cmsmIndicatorTimeframe = indicatorTimeframe; // Store for iCustom call
                        m_cmsmTimeIndexParam = timeIndexParam;
                        m_cmsmFilterInverter = filterInverter;
                        
                        // Set the primary timeframe for this provider's "once per bar" logic
                        // This should be the timeframe on which the CMSM indicator updates its labels.
                        m_timeframe = indicatorTimeframe; 

                        m_labelSearchPatternBase = "_CMSM_Label_Suggestions_"; // As per your original EA
                       }

   //--- Destructor
   virtual           ~CCMSMSignalProvider() {}

   //--- Initialization
   virtual bool      Initialize(const string forSymbol/*, const int symbolIndex, CParameters* params = NULL*/) override
                       {
                        if(!CSignalProviderBase::Initialize(forSymbol))
                           return false;
                           
                        m_lastSignalResult.calculatedOnTimeframe = m_timeframe; // CMSM indicator's update timeframe

                        // Manage shared indicator handle for the CMSM indicator
                        // This ensures the indicator is loaded on the chart and running.
                        if(s_sharedCMSMIndicatorHandle == INVALID_HANDLE)
                          {
                           s_sharedCMSMIndicatorHandle = iCustom(_Symbol,                 // Symbol for iCustom (usually current chart)
                                                                 m_cmsmIndicatorTimeframe, // Timeframe for iCustom call
                                                                 s_cmsmIndicatorPath,      // Indicator path
                                                                 // Parameters for ###CMSMIndV17.06 (1).ex5:
                                                                 m_cmsmTradeLevelParam,      // Param 1
                                                                 m_cmsmIndicatorTimeframe, // Param 2 (often indicator's TF itself)
                                                                 m_cmsmTimeIndexParam      // Param 3
                                                                 // ... ADD ALL OTHER REQUIRED PARAMS FOR YOUR CMSM.ex5 HERE ...
                                                                 );
                           
                           if(s_sharedCMSMIndicatorHandle == INVALID_HANDLE)
                             {
                              SetErrorResult(StringFormat("Failed to create shared CMSM handle on chart %s, TF %s, Path '%s'. Error: %d. Review iCustom parameters.",
                                                          _Symbol, EnumToString(m_cmsmIndicatorTimeframe), s_cmsmIndicatorPath, GetLastError()));
                              return false;
                             }
                           //PrintFormat("%s: Created shared CMSM handle %d.", GetProviderName(), s_sharedCMSMIndicatorHandle);
                          }
                        
                        s_sharedCMSMHandleUsers++;
                        m_indicatorHandle = s_sharedCMSMIndicatorHandle; // Let base class know (e.g. for IsHandleValid)
                                                                        // Actual data isn't copied via CopyBuffer for this provider type.
                        ArrayResize(m_indicatorBuffers,0); // Not used for label reading

                        //PrintFormat("%s: Initialized for symbol %s. Using shared CMSM handle %d. Users: %d", GetProviderName(), m_symbol, s_sharedCMSMIndicatorHandle, s_sharedCMSMHandleUsers);
                        return true;
                       }
                       
   //--- Deinitialization
   virtual void      Deinitialize() override
                       {
                        if(m_indicatorHandle != INVALID_HANDLE) // This instance was using the shared handle
                          {
                           s_sharedCMSMHandleUsers--;
                           if(s_sharedCMSMHandleUsers == 0 && s_sharedCMSMIndicatorHandle != INVALID_HANDLE)
                             {
                              IndicatorRelease(s_sharedCMSMIndicatorHandle);
                              s_sharedCMSMIndicatorHandle = INVALID_HANDLE;
                              //PrintFormat("%s: Released shared CMSM handle %d.", GetProviderName(), m_indicatorHandle);
                             }
                           m_indicatorHandle = INVALID_HANDLE; // This instance no longer directly refers to it for its primary logic
                          }
                        CSignalProviderBase::Deinitialize();
                       }

   //--- Calculate and return the CMSM signal by reading chart labels for m_symbol
   virtual SignalResult GetSignal() override
                       {
                        // 1. Basic Checks
                        if(!m_enabled) { SetErrorResult("Provider is disabled."); return m_lastSignalResult; }
                        // No direct indicator handle check needed for label reading if indicator is just drawing objects.
                        // However, s_sharedCMSMIndicatorHandle ensures the indicator was loaded.
                        if(s_sharedCMSMIndicatorHandle == INVALID_HANDLE && m_indicatorHandle == INVALID_HANDLE) 
                            { SetErrorResult("CMSM Indicator not loaded (shared handle invalid)."); return m_lastSignalResult; }
                        if(m_symbol == "") { SetErrorResult("Symbol not set for CMSM provider instance."); return m_lastSignalResult; }

                        // 2. "Once per bar" logic:
                        // Assumes labels are updated on new bars of the CMSM indicator's timeframe.
                        if (!IsNewCalculationNeededForBar()) { return m_lastSignalResult; }
                        
                        // Reset for new calculation attempt
                        m_lastSignalResult.isValid = false; m_lastSignalResult.direction = None;
                        m_lastSignalResult.strength = 0.0; m_lastSignalResult.errorMessage = "";
                        m_lastSignalResult.primaryIndicatorValue = EMPTY_VALUE; 
                        m_lastSignalResult.supportingPriceValue = iClose(m_symbol, PERIOD_CURRENT, 0); // Current price as context

                        // 3. --- Actual CMSM Label Reading Logic ---
                        Direction signal = None;  
                        long chart_id = ChartID(); 
                        if(chart_id == 0) 
                          { SetErrorResult("Invalid ChartID for CMSM label search."); return m_lastSignalResult;}

                        int totalObjects = (int)ObjectsTotal(chart_id, -1, OBJ_LABEL); 
                        // Construct the full search pattern for the specific symbol
                        string searchPatternFull = m_labelSearchPatternBase + m_symbol + "_"; 

                        for(int i = totalObjects - 1; i >= 0; i--)  
                          {
                           string objName = ObjectName(chart_id, i, -1, OBJ_LABEL); 
                           // Check if the object name starts with the symbol-specific pattern
                           if(StringFind(objName, searchPatternFull, 0) == 0) // Ensure it starts with the pattern
                             {
                              ResetLastError(); 
                              string labelText = ObjectGetString(chart_id, objName, OBJPROP_TEXT); 
                              int error_code = GetLastError();
                              
                              if(error_code == 0 && labelText != "")  
                                {
                                 string upperLabelText = labelText;
                                 StringToUpper(upperLabelText);
                                 // The symbol check is implicitly handled by searchPatternFull matching the start of objName
                                 
                                 if(StringFind(upperLabelText, "BUY") >= 0) { signal = Buy; break; } // Found signal, exit loop
                                 else if(StringFind(upperLabelText, "SELL") >= 0) { signal = Sell; break; } // Found signal, exit loop
                                }
                              else if(error_code != 0) 
                                { 
                                 // Log this, but don't necessarily fail the whole signal for one bad label read.
                                 // The loop will continue. If no valid label is found, signal remains None.
                                 PrintFormat("%s: Error reading text of object '%s' for symbol %s. Error: %d", GetProviderName(), objName, m_symbol, error_code); 
                                }
                             }  
                          } // End of object loop
                          
                        // Apply filter inverter
                        if(m_cmsmFilterInverter && signal != None)
                          { signal = (signal == Buy) ? Sell : Buy; }
                        
                        // 4. Populate and return the result
                        double pValue = (signal == Buy ? 1.0 : (signal == Sell ? -1.0 : 0.0)); // Simple representation
                        SetSuccessResult(signal, (signal != None ? 100.0 : 0.0), pValue, m_lastSignalResult.supportingPriceValue);
                        
                        UpdateLastSuccessfulCalculationTime(); // Mark that we've processed this bar
                           
                        return m_lastSignalResult;
                       }

   //--- Provider Type
   virtual int       Type() const override { return MQL5_PROVIDER_TYPE_ID_CMSM; }
  };

// Initialize static members
int CCMSMSignalProvider::s_sharedCMSMIndicatorHandle = INVALID_HANDLE;
int CCMSMSignalProvider::s_sharedCMSMHandleUsers     = 0;
string CCMSMSignalProvider::s_cmsmIndicatorPath      = "";
bool CCMSMSignalProvider::s_staticMembersInitialized = false;

#endif // ORTBO_CMSM_SIGNAL_PROVIDER_MQH
