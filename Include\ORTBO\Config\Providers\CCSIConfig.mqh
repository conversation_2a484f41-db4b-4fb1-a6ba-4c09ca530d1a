//+------------------------------------------------------------------+
//|                                                   CCSIConfig.mqh |
//|                                      Copyright 2025, Your Name   |
//|                                     https://www.yourwebsite.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name"
#property link      "https://www.yourwebsite.com"
#property version   "1.00"

#ifndef ORTBO_CCSI_CONFIG_MQH
#define ORTBO_CCSI_CONFIG_MQH

#include "CProviderConfigBase.mqh"

//+------------------------------------------------------------------+
//| CSI provider configuration                                       |
//+------------------------------------------------------------------+
class CCSIConfig : public CProviderConfigBase
  {
private:
   int               m_maPeriod;          // CSI MA period
   int               m_maDelta;           // CSI MA delta
   
public:
   //--- Constructor with default values
                     CCSIConfig() : m_maPeriod(20),
                                    m_maDelta(1)
     {
      m_configType = "CSI";
     }
   
   //--- Constructor with parameters
                     CCSIConfig(int maPeriod,
                                int maDelta,
                                bool filterInverter,
                                ENUM_TIMEFRAMES timeframe)
     {
      m_configType = "CSI";
      m_maPeriod = maPeriod;
      m_maDelta = maDelta;
      m_filterInverter = filterInverter;
      m_timeframe = timeframe;
      m_enabled = true;
     }
   
   //--- Validation
   virtual bool      Validate() override
     {
      string error;
      
      // Validate common parameters
      if(!ValidateCommon(error))
        {
         SetValidationError(error);
         return false;
        }
      
      // Validate CSI MA period
      if(!CConfigValidator::ValidateIntRange(m_maPeriod, 1, 200, error))
        {
         SetValidationError("CSI MA Period: " + error);
         return false;
        }
      
      // Validate CSI MA delta
      if(!CConfigValidator::ValidateIntRange(m_maDelta, 0, 50, error))
        {
         SetValidationError("CSI MA Delta: " + error);
         return false;
        }
      
      ClearValidationError();
      return true;
     }
   
   //--- Serialization
   virtual bool      LoadFromString(const string configString) override
     {
      string params[];
      int count = StringSplit(configString, '|', params);
      
      if(count < 5)
        {
         SetValidationError("Invalid configuration string format");
         return false;
        }
      
      int index = 0;
      
      // Parse common parameters
      if(!ParseCommonParams(params, index))
        {
         SetValidationError("Failed to parse common parameters");
         return false;
        }
      
      // Parse CSI-specific parameters
      m_maPeriod = (int)StringToInteger(params[index++]);
      m_maDelta = (int)StringToInteger(params[index++]);
      
      return Validate();
     }
   
   virtual string    SaveToString() const override
     {
      string common = CommonParamsToString();
      string specific = StringFormat("%d|%d", m_maPeriod, m_maDelta);
      return common + "|" + specific;
     }
   
   //--- Clone
   virtual CConfigBase* Clone() const override
     {
      CCSIConfig* clone = new CCSIConfig();
      clone.m_enabled = m_enabled;
      clone.m_timeframe = m_timeframe;
      clone.m_filterInverter = m_filterInverter;
      clone.m_maPeriod = m_maPeriod;
      clone.m_maDelta = m_maDelta;
      clone.m_configName = m_configName;
      clone.m_version = m_version;
      return clone;
     }
   
   //--- Getters
   int               GetMAPeriod() const { return m_maPeriod; }
   int               GetMADelta() const { return m_maDelta; }
   
   //--- Setters
   void              SetMAPeriod(int period) 
     { 
      m_maPeriod = period; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetMADelta(int delta) 
     { 
      m_maDelta = delta; 
      m_lastModified = TimeCurrent();
     }
  };

#endif // ORTBO_CCSI_CONFIG_MQH