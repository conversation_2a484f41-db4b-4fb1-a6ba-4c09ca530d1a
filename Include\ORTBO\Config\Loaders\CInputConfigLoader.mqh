//+------------------------------------------------------------------+
//|                                         CInputConfigLoader.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"
#property version   "1.00"

#ifndef ORTBO_CINPUT_CONFIG_LOADER_MQH
#define ORTBO_CINPUT_CONFIG_LOADER_MQH

#include <Object.mqh>
#include "../Base/CConfigBase.mqh"
#include "../CConfigFactory.mqh"
#include "../../Enums/ORTBO_Enums.mqh"

//+------------------------------------------------------------------+
//| Configuration loader from EA input parameters                   |
//+------------------------------------------------------------------+
class CInputConfigLoader : public CObject
  {
public:
   //--- Load configuration from EA inputs
   static CConfigBase* LoadConfig(ENUM_SIGNAL_SOURCE source)
     {
      // Check if the signal source is enabled
      if(!IsSourceEnabled(source))
        {
         PrintFormat("CInputConfigLoader: Signal source %s is disabled", EnumToString(source));
         return NULL;
        }
      
      return CConfigFactory::CreateFromInputs(source);
     }
   
   //--- Check if signal source is enabled in EA inputs
   static bool IsSourceEnabled(ENUM_SIGNAL_SOURCE source)
     {
      switch(source)
        {
         case SIGNAL_SRC_MA:
            return InpEnableSignalMovingAverage;
            
         case SIGNAL_SRC_RSI:
            return InpEnableSignalRSI;
            
         case SIGNAL_SRC_CSI:
            return InpEnableSignalCSI;
            
         case SIGNAL_SRC_CFFP:
            return InpEnableSignalCFFP;
            
         case SIGNAL_SRC_CMSM:
            return InpEnableSignalCMSM;
            
         default:
            return false;
        }
     }
   
   //--- Load all enabled configurations
   static bool LoadAllConfigs(CConfigBase* configs[], ENUM_SIGNAL_SOURCE sources[], int &count)
     {
      count = 0;
      
      ENUM_SIGNAL_SOURCE allSources[] = {
         SIGNAL_SRC_MA,
         SIGNAL_SRC_RSI,
         SIGNAL_SRC_CSI,
         SIGNAL_SRC_CFFP,
         SIGNAL_SRC_CMSM
      };
      
      int maxSources = ArraySize(allSources);
      ArrayResize(configs, maxSources);
      ArrayResize(sources, maxSources);
      
      for(int i = 0; i < maxSources; i++)
        {
         if(IsSourceEnabled(allSources[i]))
           {
            CConfigBase* config = LoadConfig(allSources[i]);
            if(config != NULL)
              {
               configs[count] = config;
               sources[count] = allSources[i];
               count++;
              }
           }
        }
      
      return count > 0;
     }
   
   //--- Create configuration summary string
   static string CreateConfigSummary()
     {
      string summary = "EA Input Configuration Summary:\n";
      
      if(InpEnableSignalMovingAverage)
        {
         summary += StringFormat("MA: Period=%d, Method=%s, Price=%s, Shift=%d, Margin=%d, TF=%s\n",
                                InpMaPeriod, EnumToString(InpMaMethod), EnumToString(InpMaPrice),
                                InpMaShift, InpMaMargin, EnumToString(InpMaFrame));
        }
      
      if(InpEnableSignalRSI)
        {
         summary += StringFormat("RSI: Period=%d, Min=%.1f, Max=%.1f, TF=%s\n",
                                InpRsiPeriod, InpRsiMinimum, InpRsiMaximum, EnumToString(InpRSIFrame));
        }
      
      if(InpEnableSignalCSI)
        {
         summary += StringFormat("CSI: MAPeriod=%d, MADelta=%d, TF=%s\n",
                                InpCSIMAPeriod, InpCSIMADelta, EnumToString(InpCSIFrame));
        }
      
      if(InpEnableSignalCFFP)
        {
         summary += StringFormat("CFFP: FastMA=%d, SlowMA=%d, Method=%s, Price=%s, TF=%s\n",
                                InpCFFPFastMAPeriod, InpCFFPSlowMAPeriod, EnumToString(InpCFFPMAMethod),
                                EnumToString(InpCFFPAppliedPrice), EnumToString(InpCFFPFrame));
        }
      
      if(InpEnableSignalCMSM)
        {
         summary += StringFormat("CMSM: TradeLevel=%.1f, TimeIndex=%d, TF=%s\n",
                                InpCMSM_TradeLevel, InpCMSM_TimeIndex, EnumToString(InpCMSMTimeFrame));
        }
      
      return summary;
     }
   
   //--- Validate all input configurations
   static bool ValidateAllInputs(string &errorReport)
     {
      errorReport = "";
      bool allValid = true;
      
      ENUM_SIGNAL_SOURCE sources[] = {
         SIGNAL_SRC_MA,
         SIGNAL_SRC_RSI,
         SIGNAL_SRC_CSI,
         SIGNAL_SRC_CFFP,
         SIGNAL_SRC_CMSM
      };
      
      for(int i = 0; i < ArraySize(sources); i++)
        {
         if(IsSourceEnabled(sources[i]))
           {
            CConfigBase* config = LoadConfig(sources[i]);
            if(config != NULL)
              {
               if(!config.Validate())
                 {
                  errorReport += StringFormat("%s: %s\n", EnumToString(sources[i]), config.GetValidationError());
                  allValid = false;
                 }
               delete config;
              }
            else
              {
               errorReport += StringFormat("%s: Failed to create configuration\n", EnumToString(sources[i]));
               allValid = false;
              }
           }
        }
      
      return allValid;
     }
  };

#endif // ORTBO_CINPUT_CONFIG_LOADER_MQH
