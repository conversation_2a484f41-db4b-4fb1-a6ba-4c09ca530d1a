//+------------------------------------------------------------------+
//|                                       CCSISignalProvider.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"

#ifndef ORTBO_CSI_SIGNAL_PROVIDER_MQH
#define ORTBO_CSI_SIGNAL_PROVIDER_MQH

#include "../Base/CSignalProviderBase.mqh"
#include "../ProviderTypeDefinitions.mqh"  // Central provider type definitions
// ORTBO_Enums.mqh is included via CSignalProviderBase

//+------------------------------------------------------------------+
//| Concrete Currency Strength Index (CSI) Signal Provider.          |
//| Manages a shared iCustom handle for the CSI indicator.           |
//+------------------------------------------------------------------+
class CCSISignalProvider : public CSignalProviderBase
  {
private:
   // --- Static members for shared indicator handle ---
   static int        s_sharedCSIIndicatorHandle; // Shared handle for CURRENCY_STRENGTH_INDEX.ex5
   static int        s_sharedCSIHandleUsers;     // Counter for users of the shared handle
   static string     s_csiIndicatorPath;         // Path to the CSI indicator ex5 file
   static bool       s_staticMembersInitialized; // Flag to initialize static path once

   // --- Instance-specific members ---
   // CSI parameters (used if this instance is the one creating the shared handle)
   int               m_csiMaPeriod;
   int               m_csiMaDelta;
   bool              m_csiFilterInverter;
   // Note: m_timeframe for CSI calculation is inherited from CSignalProviderBase

   // Currency mapping for this instance (for m_symbol)
   string            m_csiCurrencyNames[8]; // Order from your original CSI logic
   int               m_instanceBaseCcyIndex;  // Index in m_csiCurrencyNames for m_symbol's base currency
   int               m_instanceQuoteCcyIndex; // Index in m_csiCurrencyNames for m_symbol's quote currency
   
   // Buffer for all 8 currency strengths from the shared indicator
   // This buffer is copied into from the shared indicator, not m_indicatorBuffers from base.
   // m_indicatorBuffers in base might remain unused or resized to 0 for this provider type.
   double            m_allStrengthsBuffer[8]; 


   // --- Private static initializer for path ---
   static void       InitializeStaticMembers()
                       {
                        if(!s_staticMembersInitialized)
                          {
                           s_csiIndicatorPath = "::CURRENCY_STRENGTH_INDEX.ex5"; // Using resource path
                           s_staticMembersInitialized = true;
                          }
                       }
public:
   //--- Constructor
                     CCSISignalProvider(
                                     const int maPeriod,
                                     const int maDelta,
                                     const bool filterInverter,
                                     const ENUM_TIMEFRAMES signalTimeframe
                                     )
                       : CSignalProviderBase("CSI") // Call base constructor
                       {
                        InitializeStaticMembers(); // Ensure static path is set

                        m_csiMaPeriod = maPeriod;
                        m_csiMaDelta = maDelta;
                        m_csiFilterInverter = filterInverter;
                        m_timeframe = signalTimeframe; // Timeframe for the underlying CSI indicator

                        // Initialize CSI currency names array (matching your original EA's m_CurrencyNames)
                        m_csiCurrencyNames[0] = "EUR"; m_csiCurrencyNames[1] = "GBP";
                        m_csiCurrencyNames[2] = "AUD"; m_csiCurrencyNames[3] = "NZD";
                        m_csiCurrencyNames[4] = "USD"; m_csiCurrencyNames[5] = "CAD";
                        m_csiCurrencyNames[6] = "CHF"; m_csiCurrencyNames[7] = "JPY";
                        
                        m_instanceBaseCcyIndex = -1;
                        m_instanceQuoteCcyIndex = -1;
                        
                        ArrayInitialize(m_allStrengthsBuffer, EMPTY_VALUE);
                       }

   //--- Destructor
   virtual           ~CCSISignalProvider()
                       {
                        // Deinitialize should be called by manager to properly manage shared handle
                        // If not, this destructor won't affect the static shared handle count by itself
                       }

   //--- Initialization
   virtual bool      Initialize(const string forSymbol/*, const int symbolIndex, CParameters* params = NULL*/) override
                       {
                        if(!CSignalProviderBase::Initialize(forSymbol)) // Calls base, sets m_symbol
                           return false;
                           
                        m_lastSignalResult.calculatedOnTimeframe = m_timeframe; // CSI indicator's timeframe

                        // Map currencies for this specific symbol instance
                        if(!MapInstanceCurrencyIndices())
                          {
                           // SetErrorResult already called by MapInstanceCurrencyIndices if it fails
                           return false;
                          }

                        // Manage shared indicator handle
                        if(s_sharedCSIIndicatorHandle == INVALID_HANDLE)
                          {
                           // Create the shared handle using _Symbol (EA's chart symbol) as CSI is often global
                           // The m_timeframe is the one specified for the CSI indicator itself.
                           s_sharedCSIIndicatorHandle = iCustom(_Symbol, m_timeframe, s_csiIndicatorPath, m_csiMaPeriod, m_csiMaDelta);
                           
                           if(s_sharedCSIIndicatorHandle == INVALID_HANDLE)
                             {
                              SetErrorResult(StringFormat("Failed to create shared CSI handle on chart %s, TF %s, Path %s. Error: %d",
                                                          _Symbol, EnumToString(m_timeframe), s_csiIndicatorPath, GetLastError()));
                              return false;
                             }
                           //PrintFormat("%s: Created shared CSI handle %d for %s", GetProviderName(), s_sharedCSIIndicatorHandle, s_csiIndicatorPath);
                          }
                        
                        s_sharedCSIHandleUsers++; // Increment user count for this instance
                        m_indicatorHandle = s_sharedCSIIndicatorHandle; // Let base class know about the handle for IsHandleValid checks etc.
                                                                        // though data copying will use s_sharedCSIIndicatorHandle directly.

                        // For this provider, m_indicatorBuffers from base class might not be directly used
                        // as we copy all 8 strengths into m_allStrengthsBuffer.
                        // We can resize it to 1 minimum to avoid allocation issues.
                        if(ArrayResize(m_indicatorBuffers, 1) != 1)
                          {
                           SetErrorResult(StringFormat("Failed to resize m_indicatorBuffers for CSI provider on %s.", m_symbol));
                           return false;
                          } 

                        //PrintFormat("%s: Initialized for symbol %s. Using shared CSI handle %d. Users: %d", GetProviderName(), m_symbol, s_sharedCSIIndicatorHandle, s_sharedCSIHandleUsers);
                        return true;
                       }
                       
   //--- Deinitialization
   virtual void      Deinitialize() override
                       {
                        if(m_indicatorHandle != INVALID_HANDLE) // if this instance was successfully initialized
                          {
                           s_sharedCSIHandleUsers--;
                           if(s_sharedCSIHandleUsers == 0 && s_sharedCSIIndicatorHandle != INVALID_HANDLE)
                             {
                              IndicatorRelease(s_sharedCSIIndicatorHandle);
                              s_sharedCSIIndicatorHandle = INVALID_HANDLE;
                              //PrintFormat("%s: Released shared CSI handle %d.", GetProviderName(), m_indicatorHandle);
                             }
                           m_indicatorHandle = INVALID_HANDLE; // This instance no longer refers to it
                          }
                        CSignalProviderBase::Deinitialize(); // Call base deinit
                       }

   //--- Calculate and return the CSI signal for m_symbol
   virtual SignalResult GetSignal() override
                       {
                        // 1. Basic Checks
                        if(!m_enabled)
                          { SetErrorResult("Provider is disabled."); return m_lastSignalResult; }
                        if(s_sharedCSIIndicatorHandle == INVALID_HANDLE) // Check shared handle
                          { SetErrorResult("Shared CSI Indicator handle is invalid."); return m_lastSignalResult; }
                        if(m_symbol == "")
                          { SetErrorResult("Symbol not set for CSI provider instance."); return m_lastSignalResult; }
                        if(m_instanceBaseCcyIndex < 0 || m_instanceQuoteCcyIndex < 0)
                          { SetErrorResult(StringFormat("Currency indices not mapped for symbol %s.", m_symbol)); return m_lastSignalResult; }

                        // 2. "Once per bar" logic for the underlying CSI indicator's timeframe
                        if (!IsNewCalculationNeededForBar()) // Uses m_timeframe of the CSI indicator
                          { return m_lastSignalResult; }
                        
                        m_lastSignalResult.isValid = false; m_lastSignalResult.direction = None; // Reset for new calc attempt
                        m_lastSignalResult.strength = 0.0; m_lastSignalResult.errorMessage = "";
                        m_lastSignalResult.primaryIndicatorValue = EMPTY_VALUE; m_lastSignalResult.supportingPriceValue = EMPTY_VALUE;


                        // 3. Check if shared indicator is ready
                        if(BarsCalculated(s_sharedCSIIndicatorHandle) < 2) // Need at least 2 bars for meaningful data from most indicators
                          {
                           SetErrorResult(StringFormat("Shared CSI indicator not ready (BarsCalculated: %d < 2).", BarsCalculated(s_sharedCSIIndicatorHandle)));
                           return m_lastSignalResult;
                          }

                        // 4. Copy all 8 currency strength values from the shared indicator
                        // Data is from the last completed bar (shift 1)
                        bool allCopied = true;
                        double tempSingleBuffer[1]; // Temporary buffer for CopyBuffer
                        ArraySetAsSeries(tempSingleBuffer, true);

                        for(int i = 0; i < 8; i++)
                          {
                           ResetLastError();
                           int copied = CopyBuffer(s_sharedCSIIndicatorHandle, i, 1, 1, tempSingleBuffer);
                           if(copied <= 0)
                             {
                              SetErrorResult(StringFormat("Failed to copy CSI buffer %d (%s) for symbol %s. Handle %d. Copied %d, Err %d",
                                                         i, m_csiCurrencyNames[i], m_symbol, s_sharedCSIIndicatorHandle, copied, GetLastError()));
                              allCopied = false;
                              break;
                             }
                           m_allStrengthsBuffer[i] = tempSingleBuffer[0];
                          }

                        if(!allCopied) { return m_lastSignalResult; } // Error already set

                        // 5. --- Actual CSI Signal Calculation for m_symbol ---
                        // Using m_allStrengthsBuffer, m_instanceBaseCcyIndex, m_instanceQuoteCcyIndex
                        // Replicate your existing CSI logic (Original and S/W with price confirmation)
                        
                        Direction signal = None;
                        // Get close prices for confirmation (from chart's current period for m_symbol)
                        double prevChartClose = iClose(m_symbol, PERIOD_CURRENT, 2);
                        double currChartClose = iClose(m_symbol, PERIOD_CURRENT, 1);
                        bool pricesValid = (prevChartClose != 0.0 && currChartClose != 0.0 && TerminalInfoInteger(TERMINAL_CONNECTED));

                        // Determine overall strongest (maxStrengthIdx) and weakest (minStrengthIdx) currencies from m_allStrengthsBuffer
                        double maxStrengthVal = -DBL_MAX; int maxStrengthBufferIdx = -1;
                        double minStrengthVal = DBL_MAX;  int minStrengthBufferIdx = -1;
                        for(int j = 0; j < 8; j++)
                          {
                           if(m_allStrengthsBuffer[j] > maxStrengthVal) { maxStrengthVal = m_allStrengthsBuffer[j]; maxStrengthBufferIdx = j; }
                           if(m_allStrengthsBuffer[j] < minStrengthVal) { minStrengthVal = m_allStrengthsBuffer[j]; minStrengthBufferIdx = j; }
                          }
                          
                        // Store base and quote strengths for result
                        double baseStr = (m_instanceBaseCcyIndex >=0) ? m_allStrengthsBuffer[m_instanceBaseCcyIndex] : EMPTY_VALUE;
                        double quoteStr = (m_instanceQuoteCcyIndex >=0) ? m_allStrengthsBuffer[m_instanceQuoteCcyIndex] : EMPTY_VALUE;
                        m_lastSignalResult.primaryIndicatorValue = baseStr; // Or a combined metric
                        m_lastSignalResult.supportingPriceValue = quoteStr; // Or currChartClose

                        // --- Logic 1: Original CSI Logic (Prioritized) ---
                        Direction signal_original = None;
                        if(pricesValid)
                          {
                           // Your logic: Base is strongest, Quote is weakest -> Sell
                           if (m_instanceBaseCcyIndex == maxStrengthBufferIdx && m_instanceQuoteCcyIndex == minStrengthBufferIdx)
                               signal_original = Sell; 
                           // Your logic: Base is weakest, Quote is strongest -> Buy
                           else if (m_instanceBaseCcyIndex == minStrengthBufferIdx && m_instanceQuoteCcyIndex == maxStrengthBufferIdx)
                               signal_original = Buy; 
                          }
                        else // No valid prices for Original Logic confirmation
                          {
                           // PrintFormat("%s WARN: No valid close prices for Original CSI confirm on %s. Using pure strength.", GetProviderName(), m_symbol);
                           if (m_instanceBaseCcyIndex == maxStrengthBufferIdx && m_instanceQuoteCcyIndex == minStrengthBufferIdx) signal_original = Sell;
                           else if (m_instanceBaseCcyIndex == minStrengthBufferIdx && m_instanceQuoteCcyIndex == maxStrengthBufferIdx) signal_original = Buy;
                          }

                        if (signal_original != None)
                          {
                           signal = signal_original;
                          }
                        else // Original Logic yielded None, try S/W Logic
                          {
                           Direction signal_sw_strength_based = None;
                           // Your S/W Logic (example structure, adapt from your EA):
                           // Scenario 1: Based on the universally STRONGEST currency (maxStrengthBufferIdx)
                           if (pricesValid && currChartClose < prevChartClose && m_instanceQuoteCcyIndex == maxStrengthBufferIdx && baseStr < 0) // Price falling, Quote is strongest, Base is weak
                             { signal_sw_strength_based = Buy; }
                           else if (pricesValid && currChartClose > prevChartClose && m_instanceBaseCcyIndex == maxStrengthBufferIdx && quoteStr < 0) // Price rising, Base is strongest, Quote is weak
                             { signal_sw_strength_based = Sell; }
                           // Scenario 2: Based on the universally WEAKEST currency (minStrengthBufferIdx)
                           else if (pricesValid && currChartClose < prevChartClose && m_instanceBaseCcyIndex == minStrengthBufferIdx && quoteStr > 0) // Price falling, Base is weakest, Quote is strong
                             { signal_sw_strength_based = Buy; }
                           else if (pricesValid && currChartClose > prevChartClose && m_instanceQuoteCcyIndex == minStrengthBufferIdx && baseStr > 0) // Price rising, Quote is weakest, Base is strong
                             { signal_sw_strength_based = Sell; }
                           
                           // If S/W logic produced a signal (even without price confirmation if pricesValid was false for S/W specific conditions)
                           signal = signal_sw_strength_based; // For now, directly assign. Your EA might have more complex S/W confirmation.
                          }

                        // Apply filter inverter
                        if(m_csiFilterInverter && signal != None)
                          {
                           signal = (signal == Buy) ? Sell : Buy;
                          }
                        
                        // 6. Populate and return the result
                        double strength = (signal != None) ? 100.0 : 0.0; // Basic strength
                        SetSuccessResult(signal, strength, baseStr, currChartClose); // Using baseStr as primary, currChartClose as supporting
                                                                                      // You might want a combined strength value as primary.
                        UpdateLastSuccessfulCalculationTime(); 
                           
                        return m_lastSignalResult;
                       }

   //--- Provider Type
   virtual int       Type() const override { return MQL5_PROVIDER_TYPE_ID_CSI; }

private:
   //--- Maps the instance's m_symbol to base/quote currency indices from m_csiCurrencyNames
   bool              MapInstanceCurrencyIndices()
                       {
                        string baseCcyName = SymbolInfoString(m_symbol, SYMBOL_CURRENCY_BASE);
                        string quoteCcyName = SymbolInfoString(m_symbol, SYMBOL_CURRENCY_PROFIT);

                        if(baseCcyName == NULL || baseCcyName == "" || quoteCcyName == NULL || quoteCcyName == "")
                          {
                           SetErrorResult(StringFormat("Failed to get Base/Quote currency for %s. Error: %d", m_symbol, GetLastError()));
                           return false;
                          }

                        m_instanceBaseCcyIndex = -1;
                        m_instanceQuoteCcyIndex = -1;

                        for(int j=0; j<8; j++)
                          {
                           if(m_csiCurrencyNames[j] == baseCcyName) m_instanceBaseCcyIndex = j;
                           if(m_csiCurrencyNames[j] == quoteCcyName) m_instanceQuoteCcyIndex = j;
                          }

                        if(m_instanceBaseCcyIndex < 0 || m_instanceQuoteCcyIndex < 0)
                          {
                           SetErrorResult(StringFormat("Cannot map Base (%s) or Quote (%s) for symbol '%s' to CSI buffers.",
                                                       (m_instanceBaseCcyIndex < 0 ? baseCcyName : "-"),
                                                       (m_instanceQuoteCcyIndex < 0 ? quoteCcyName : "-"),
                                                       m_symbol));
                           return false;
                          }
                        //PrintFormat("%s: Mapped %s to BaseIdx:%d (%s), QuoteIdx:%d (%s)", GetProviderName(), m_symbol, m_instanceBaseCcyIndex, baseCcyName, m_instanceQuoteCcyIndex, quoteCcyName);
                        return true;
                       }
  };

// Initialize static members
int CCSISignalProvider::s_sharedCSIIndicatorHandle = INVALID_HANDLE;
int CCSISignalProvider::s_sharedCSIHandleUsers     = 0;
string CCSISignalProvider::s_csiIndicatorPath      = ""; // Will be set by InitializeStaticMembers
bool CCSISignalProvider::s_staticMembersInitialized = false;


#endif // ORTBO_CSI_SIGNAL_PROVIDER_MQH
