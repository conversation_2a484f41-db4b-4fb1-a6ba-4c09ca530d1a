//+------------------------------------------------------------------+
//|                                          CSignalManager.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"

#ifndef ORTBO_CSIGNAL_MANAGER_FULL_MQH
#define ORTBO_CSIGNAL_MANAGER_FULL_MQH

#include <Object.mqh>
#include <Arrays/ArrayObj.mqh>    // For CArrayObj
#include "Base/CSignalProviderBase.mqh"
#include "Base/SignalResult.mqh"
#include "ProviderTypeDefinitions.mqh"  // Central provider type definitions
// ORTBO_Enums.mqh is included via CSignalProviderBase -> ISignalProvider -> SignalResult

//+------------------------------------------------------------------+
//| CSymbolContextProviders: Holds all signal providers for one symbol.|
//+------------------------------------------------------------------+
class CSymbolContextProviders : public CObject
  {
public:
   string      m_symbol;
   CArrayObj* m_providers; // Stores CSignalProviderBase* pointers (which inherit from CObject)

               CSymbolContextProviders(const string symbol_name)
                 {
                  m_symbol = symbol_name;
                  m_providers = new CArrayObj();
                  if(CheckPointer(m_providers) == POINTER_DYNAMIC)
                    {
                     m_providers.FreeMode(true); // CArrayObj will own and delete the ISignalProvider objects
                    }
                  else
                    {
                     PrintFormat("CSymbolContextProviders Error: Failed to allocate m_providers CArrayObj for symbol %s", m_symbol);
                    }
                 }

   virtual    ~CSymbolContextProviders() override
                 {
                  // m_providers CArrayObj (if FreeMode is true) will delete its ISignalProvider objects
                  // when it itself is deleted or Shutdown() is called.
                  if(CheckPointer(m_providers) != POINTER_INVALID)
                    {
                     // m_providers.Shutdown(); // Not strictly needed here if CArrayObj is deleted
                     delete m_providers; 
                     m_providers = NULL;
                    }
                 }

   // Adds a provider. Returns false if provider is null or add fails.
   bool        AddProvider(CSignalProviderBase* provider)
                 {
                  if(CheckPointer(provider) == POINTER_INVALID || CheckPointer(m_providers) == POINTER_INVALID)
                     return false;
                  return m_providers.Add((CObject*)provider);
                 }

   // Gets a provider by its type ID. Returns NULL if not found.
   CSignalProviderBase* GetProvider(const int providerTypeId)
                 {
                  if(CheckPointer(m_providers) == POINTER_INVALID) return NULL;
                  for(int i = 0; i < m_providers.Total(); i++)
                    {
                     CSignalProviderBase* p = (CSignalProviderBase*)m_providers.At(i);
                     if(CheckPointer(p) != POINTER_INVALID && p.Type() == providerTypeId)
                       {
                        return p;
                       }
                    }
                  return NULL;
                 }
                 
   // Calls Deinitialize on all managed providers
   void DeinitializeAllProviders()
                 {
                    if(CheckPointer(m_providers) == POINTER_INVALID) return;
                    for(int i = 0; i < m_providers.Total(); i++)
                    {
                       CSignalProviderBase* p = (CSignalProviderBase*)m_providers.At(i);
                       if(CheckPointer(p) != POINTER_INVALID)
                       {
                          p.Deinitialize();
                       }
                    }
                 }
  };

//+------------------------------------------------------------------+
//| CSignalManager: Manages signal providers for multiple symbols.   |
//+------------------------------------------------------------------+
class CSignalManager : public CObject
  {
private:
   CArrayObj* m_allSymbolContexts; // Stores CSymbolContextProviders* pointers

   // Helper to find a symbol's context object
   CSymbolContextProviders* GetSymbolContext(const string forSymbol) const
                 {
                  if(CheckPointer(m_allSymbolContexts) == POINTER_INVALID) return NULL;
                  for(int i = 0; i < m_allSymbolContexts.Total(); i++)
                    {
                     CSymbolContextProviders* ctx = (CSymbolContextProviders*)m_allSymbolContexts.At(i);
                     if(CheckPointer(ctx) != POINTER_INVALID && ctx.m_symbol == forSymbol)
                       {
                        return ctx;
                       }
                    }
                  return NULL;
                 }
                 
   // Helper to map ENUM_SIGNAL_SOURCE to provider Type ID for basic signals
   int MapSourceToProviderType(ENUM_SIGNAL_SOURCE source) const
                 {
                    switch(source)
                    {
                       case SIGNAL_SRC_MA:    return MQL5_PROVIDER_TYPE_ID_MA;
                       case SIGNAL_SRC_RSI:   return MQL5_PROVIDER_TYPE_ID_RSI;
                       case SIGNAL_SRC_CSI:   return MQL5_PROVIDER_TYPE_ID_CSI;
                       case SIGNAL_SRC_CFFP:  return MQL5_PROVIDER_TYPE_ID_CFFP;
                       case SIGNAL_SRC_CMSM:  return MQL5_PROVIDER_TYPE_ID_CMSM;
                       default:               return 0; // 0 indicates unknown or composite
                    }
                 }

public:
   //--- Constructor
                     CSignalManager()
                       {
                        m_allSymbolContexts = new CArrayObj();
                        if(CheckPointer(m_allSymbolContexts) != POINTER_INVALID)
                          {
                           m_allSymbolContexts.FreeMode(true); // Owns CSymbolContextProviders objects
                          }
                        else
                          {
                           Print("CSignalManager FATAL Error: Could not allocate m_allSymbolContexts CArrayObj.");
                           // This is a critical failure; the manager won't work.
                          }
                       }

   //--- Destructor
   virtual           ~CSignalManager() override
                       {
                        // CArrayObj FreeMode(true) handles deleting CSymbolContextProviders,
                        // which in turn delete their owned ISignalProvider objects.
                        if(CheckPointer(m_allSymbolContexts) != POINTER_INVALID)
                          {
                           // m_allSymbolContexts.Shutdown(); // Not strictly needed if deleting the CArrayObj
                           delete m_allSymbolContexts;
                           m_allSymbolContexts = NULL;
                          }
                       }

   //--- Initializes the manager for a list of symbols provided by the EA
   bool              InitializeManager(const string &symbols[], const int numSymbols)
                       {
                        if(CheckPointer(m_allSymbolContexts) == POINTER_INVALID) 
                          {
                           Print("CSignalManager Error: m_allSymbolContexts is NULL during InitializeManager.");
                           return false; // Critical failure if CArrayObj wasn't created
                          }
                        
                        DeinitializeManager(); // Clear any previous state and providers

                        if(numSymbols <= 0)
                          {
                           Print("CSignalManager Error: No symbols provided for initialization.");
                           return false;
                          }

                        for(int i = 0; i < numSymbols; i++)
                          {
                           if(symbols[i] == "" || symbols[i] == NULL)
                           {
                              PrintFormat("CSignalManager Warning: Empty or NULL symbol at index %d during initialization. Skipping.", i);
                              continue;
                           }
                           CSymbolContextProviders* newCtx = new CSymbolContextProviders(symbols[i]);
                           if(CheckPointer(newCtx) == POINTER_INVALID || CheckPointer(newCtx.m_providers) == POINTER_INVALID)
                             {
                              PrintFormat("CSignalManager Error: Failed to create CSymbolContextProviders object or its internal provider list for symbol %s.", symbols[i]);
                              if(CheckPointer(newCtx)!=POINTER_INVALID) delete newCtx; // Clean up partially created context
                              // Consider cleaning up already added contexts if one fails mid-loop
                              DeinitializeManager(); // Clean up everything on partial failure
                              return false;
                             }
                           if(!m_allSymbolContexts.Add(newCtx))
                             {
                               PrintFormat("CSignalManager Error: Failed to add context for symbol %s to m_allSymbolContexts.", symbols[i]);
                               delete newCtx; // Clean up object that couldn't be added
                               DeinitializeManager(); // Clean up everything on partial failure
                               return false;
                             }
                          }
                        PrintFormat("CSignalManager: Initialized for %d symbol contexts.", m_allSymbolContexts.Total());
                        return true;
                       }

   //--- Deinitializes all providers and clears symbol contexts
   void              DeinitializeManager()
                       {
                        if(CheckPointer(m_allSymbolContexts) == POINTER_INVALID) return;
                        
                        // Call Deinitialize on each provider within each context
                        for(int i = 0; i < m_allSymbolContexts.Total(); i++)
                        {
                           CSymbolContextProviders* ctx = (CSymbolContextProviders*)m_allSymbolContexts.At(i);
                           if(CheckPointer(ctx) != POINTER_INVALID)
                           {
                              ctx.DeinitializeAllProviders(); 
                           }
                        }
                        // Clear will call destructors of CSymbolContextProviders,
                        // which will call destructors of their CArrayObj (m_providers),
                        // which (if FreeMode=true) will call destructors of ISignalProvider objects.
                        m_allSymbolContexts.Clear(); 
                        // Print("CSignalManager: Deinitialized all symbol contexts and providers.");
                       }

   //--- Adds a pre-created provider instance for a specific symbol.
   //--- The provider should already be new'd up (e.g., by a factory).
   //--- The SignalManager (via CSymbolContextProviders' CArrayObj) will take ownership and delete it.
   bool              AddProviderForSymbol(const string forSymbol, CSignalProviderBase* providerToAdd)
                       {
                        if(CheckPointer(providerToAdd) == POINTER_INVALID)
                          {
                           PrintFormat("CSignalManager Error: Attempted to add NULL provider for %s.", forSymbol);
                           return false;
                          }

                        CSymbolContextProviders* ctx = GetSymbolContext(forSymbol);
                        if(CheckPointer(ctx) == POINTER_INVALID)
                          {
                           PrintFormat("CSignalManager Error: No context found for symbol %s. Cannot add provider '%s'.", forSymbol, providerToAdd.GetProviderName());
                           delete providerToAdd; // Manager can't store it, so delete to prevent leak.
                           return false;
                          }
                        
                        // Initialize the provider for this specific symbol context.
                        // The second argument to Initialize (symbolIndex) is not currently used by base/concrete providers.
                        if(!providerToAdd.Initialize(forSymbol/*, 0 (dummy index) */))
                        {
                           PrintFormat("CSignalManager Error: Failed to initialize provider '%s' for symbol %s.", providerToAdd.GetProviderName(), forSymbol);
                           delete providerToAdd; // Initialization failed, delete.
                           return false;
                        }

                        if(!ctx.AddProvider(providerToAdd))
                          {
                           PrintFormat("CSignalManager Error: Failed to add provider '%s' to context for symbol %s.", providerToAdd.GetProviderName(), forSymbol);
                           providerToAdd.Deinitialize(); // Deinitialize if add failed
                           delete providerToAdd;       // and delete.
                           return false;
                          }
                        //PrintFormat("CSignalManager: Added provider '%s' for symbol %s.", providerToAdd.GetProviderName(), forSymbol);
                        return true;
                       }
                       
   //--- Retrieves a signal result for a given symbol and source type.
   SignalResult      GetSignalResult(const string forSymbol, const ENUM_SIGNAL_SOURCE requestedSource)
                       {
                        SignalResult result; 
                        result.symbol = forSymbol;
                        result.providerName = EnumToString(requestedSource); // Tentative name for composites

                        if(IsCompositeSource(requestedSource))
                          {
                           return GetCompositeSignalResult(forSymbol, requestedSource);
                          }

                        int providerTypeID = MapSourceToProviderType(requestedSource);
                        if(providerTypeID == 0)
                          {
                           result.SetError(StringFormat("Signal source %s is not a recognized basic provider type for %s.", EnumToString(requestedSource), forSymbol));
                           return result;
                          }
                        
                        CSymbolContextProviders* ctx = GetSymbolContext(forSymbol);
                        if(CheckPointer(ctx) == POINTER_INVALID)
                          {
                           result.SetError(StringFormat("No signal context found for symbol %s when requesting %s.", forSymbol, EnumToString(requestedSource)));
                           return result;
                          }

                        CSignalProviderBase* provider = ctx.GetProvider(providerTypeID);
                        if(CheckPointer(provider) == POINTER_INVALID)
                          {
                           result.SetError(StringFormat("Provider for source %s (TypeID %d) not found for symbol %s.", EnumToString(requestedSource), providerTypeID, forSymbol));
                           return result;
                          }
                        
                        // Provider.GetSignal() handles IsEnabled check and returns appropriate SignalResult
                        return provider.GetSignal(); 
                       }
private:
    //--- Helper to check if a source is composite
    bool IsCompositeSource(const ENUM_SIGNAL_SOURCE source) const
    {
        switch(source)
        {
            // List all your composite ENUM_SIGNAL_SOURCE values here
            case SIGNAL_SRC_MA_AND_RSI:
            case SIGNAL_SRC_MA_AND_CSI:
            case SIGNAL_SRC_MA_AND_CFFP:
            case SIGNAL_SRC_RSI_AND_CSI:
            case SIGNAL_SRC_RSI_AND_CFFP:
            case SIGNAL_SRC_CSI_AND_CFFP:
            case SIGNAL_SRC_RSI_CSI_AND_CFFP:
            case SIGNAL_SRC_RSI_CSI_AND_CMSM:
            case SIGNAL_SRC_CFFP_CMSM_AND_RSI:
            case SIGNAL_SRC_CFFP_CMSM_AND_CSI:
                return true;
            default:
                return false;
        }
    }

    //--- Handles calculation of composite signals
    SignalResult GetCompositeSignalResult(const string forSymbol, const ENUM_SIGNAL_SOURCE compositeSource)
    {
        SignalResult finalResult;
        finalResult.symbol = forSymbol;
        finalResult.providerName = EnumToString(compositeSource); 
        finalResult.calculatedOnTimeframe = PERIOD_CURRENT; // Composites are effectively on current tick

        Direction dir1 = None, dir2 = None, dir3 = None;
        SignalResult res1, res2, res3;
        // For composites, we need to check validity of component signals
        
        // --- Logic for 2-way AND combinations ---
        if(compositeSource == SIGNAL_SRC_MA_AND_RSI)
        {
            res1 = GetSignalResult(forSymbol, SIGNAL_SRC_MA);  
            res2 = GetSignalResult(forSymbol, SIGNAL_SRC_RSI); 
            if(res1.isValid && res2.isValid) { // Both components must be valid
                if(res1.direction == Buy && res2.direction == Buy) finalResult.SetSuccess(Buy);
                else if(res1.direction == Sell && res2.direction == Sell) finalResult.SetSuccess(Sell);
                else finalResult.SetSuccess(None); // Valid calculation, but no combined signal
            } else { finalResult.SetError("MA_AND_RSI: One or more component signals invalid. MA: "+res1.errorMessage + " RSI: " + res2.errorMessage); }
        }
        else if(compositeSource == SIGNAL_SRC_MA_AND_CSI)
        {
            res1 = GetSignalResult(forSymbol, SIGNAL_SRC_MA);  
            res2 = GetSignalResult(forSymbol, SIGNAL_SRC_CSI); 
            if(res1.isValid && res2.isValid) {
                if(res1.direction == Buy && res2.direction == Buy) finalResult.SetSuccess(Buy);
                else if(res1.direction == Sell && res2.direction == Sell) finalResult.SetSuccess(Sell);
                else finalResult.SetSuccess(None);
            } else { finalResult.SetError("MA_AND_CSI: Component signal error. MA: "+res1.errorMessage + " CSI: " + res2.errorMessage); }
        }
        else if(compositeSource == SIGNAL_SRC_MA_AND_CFFP)
        {
            res1 = GetSignalResult(forSymbol, SIGNAL_SRC_MA);
            res2 = GetSignalResult(forSymbol, SIGNAL_SRC_CFFP);
            if(res1.isValid && res2.isValid) {
                if(res1.direction == Buy && res2.direction == Buy) finalResult.SetSuccess(Buy);
                else if(res1.direction == Sell && res2.direction == Sell) finalResult.SetSuccess(Sell);
                else finalResult.SetSuccess(None);
            } else { finalResult.SetError("MA_AND_CFFP: Component signal error. MA: "+res1.errorMessage + " CFFP: " + res2.errorMessage); }
        }
        else if(compositeSource == SIGNAL_SRC_RSI_AND_CSI)
        {
            res1 = GetSignalResult(forSymbol, SIGNAL_SRC_RSI);
            res2 = GetSignalResult(forSymbol, SIGNAL_SRC_CSI);
            if(res1.isValid && res2.isValid) {
                if(res1.direction == Buy && res2.direction == Buy) finalResult.SetSuccess(Buy);
                else if(res1.direction == Sell && res2.direction == Sell) finalResult.SetSuccess(Sell);
                else finalResult.SetSuccess(None);
            } else { finalResult.SetError("RSI_AND_CSI: Component signal error. RSI: "+res1.errorMessage + " CSI: " + res2.errorMessage); }
        }
         else if(compositeSource == SIGNAL_SRC_RSI_AND_CFFP)
        {
            res1 = GetSignalResult(forSymbol, SIGNAL_SRC_RSI);
            res2 = GetSignalResult(forSymbol, SIGNAL_SRC_CFFP);
            if(res1.isValid && res2.isValid) {
                if(res1.direction == Buy && res2.direction == Buy) finalResult.SetSuccess(Buy);
                else if(res1.direction == Sell && res2.direction == Sell) finalResult.SetSuccess(Sell);
                else finalResult.SetSuccess(None);
            } else { finalResult.SetError("RSI_AND_CFFP: Component signal error. RSI: "+res1.errorMessage + " CFFP: " + res2.errorMessage); }
        }
        else if(compositeSource == SIGNAL_SRC_CSI_AND_CFFP)
        {
            res1 = GetSignalResult(forSymbol, SIGNAL_SRC_CSI);
            res2 = GetSignalResult(forSymbol, SIGNAL_SRC_CFFP);
            if(res1.isValid && res2.isValid) {
                if(res1.direction == Buy && res2.direction == Buy) finalResult.SetSuccess(Buy);
                else if(res1.direction == Sell && res2.direction == Sell) finalResult.SetSuccess(Sell);
                else finalResult.SetSuccess(None);
            } else { finalResult.SetError("CSI_AND_CFFP: Component signal error. CSI: "+res1.errorMessage + " CFFP: " + res2.errorMessage); }
        }
        // --- Logic for 3-way AND combinations ---
        else if(compositeSource == SIGNAL_SRC_RSI_CSI_AND_CFFP) // (RSI & CSI) & CFFP
        {
            res1 = GetSignalResult(forSymbol, SIGNAL_SRC_RSI); 
            res2 = GetSignalResult(forSymbol, SIGNAL_SRC_CSI); 
            res3 = GetSignalResult(forSymbol, SIGNAL_SRC_CFFP);
            if(res1.isValid && res2.isValid && res3.isValid){
                bool entrySignalBuy = (res1.direction == Buy && res2.direction == Buy);
                bool entrySignalSell = (res1.direction == Sell && res2.direction == Sell);
                if (entrySignalBuy && res3.direction == Buy) finalResult.SetSuccess(Buy);
                else if (entrySignalSell && res3.direction == Sell) finalResult.SetSuccess(Sell);
                else finalResult.SetSuccess(None);
            } else { finalResult.SetError("RSI_CSI_AND_CFFP: Component signal error. RSI: "+res1.errorMessage + " CSI: " + res2.errorMessage + " CFFP: " + res3.errorMessage); }
        }
        else if(compositeSource == SIGNAL_SRC_RSI_CSI_AND_CMSM) // (RSI & CSI) & CMSM
        {
            res1 = GetSignalResult(forSymbol, SIGNAL_SRC_RSI); 
            res2 = GetSignalResult(forSymbol, SIGNAL_SRC_CSI); 
            res3 = GetSignalResult(forSymbol, SIGNAL_SRC_CMSM);
            if(res1.isValid && res2.isValid && res3.isValid){
                bool entrySignalBuy = (res1.direction == Buy && res2.direction == Buy);
                bool entrySignalSell = (res1.direction == Sell && res2.direction == Sell);
                if (entrySignalBuy && res3.direction == Buy) finalResult.SetSuccess(Buy);
                else if (entrySignalSell && res3.direction == Sell) finalResult.SetSuccess(Sell);
                else finalResult.SetSuccess(None);
            } else { finalResult.SetError("RSI_CSI_AND_CMSM: Component signal error. RSI: "+res1.errorMessage + " CSI: " + res2.errorMessage + " CMSM: " + res3.errorMessage); }
        }
        else if(compositeSource == SIGNAL_SRC_CFFP_CMSM_AND_RSI) // (CFFP & CMSM) & RSI
        {
            res1 = GetSignalResult(forSymbol, SIGNAL_SRC_CFFP); 
            res2 = GetSignalResult(forSymbol, SIGNAL_SRC_CMSM); 
            res3 = GetSignalResult(forSymbol, SIGNAL_SRC_RSI);
            if(res1.isValid && res2.isValid && res3.isValid){
                bool trendSignalBuy = (res1.direction == Buy && res2.direction == Buy);
                bool trendSignalSell = (res1.direction == Sell && res2.direction == Sell);
                if (trendSignalBuy && res3.direction == Buy) finalResult.SetSuccess(Buy);
                else if (trendSignalSell && res3.direction == Sell) finalResult.SetSuccess(Sell);
                else finalResult.SetSuccess(None);
            } else { finalResult.SetError("CFFP_CMSM_AND_RSI: Component signal error. CFFP: "+res1.errorMessage + " CMSM: " + res2.errorMessage + " RSI: " + res3.errorMessage); }
        }
        else if(compositeSource == SIGNAL_SRC_CFFP_CMSM_AND_CSI) // (CFFP & CMSM) & CSI
        {
            res1 = GetSignalResult(forSymbol, SIGNAL_SRC_CFFP); 
            res2 = GetSignalResult(forSymbol, SIGNAL_SRC_CMSM); 
            res3 = GetSignalResult(forSymbol, SIGNAL_SRC_CSI);
            if(res1.isValid && res2.isValid && res3.isValid){
                bool trendSignalBuy = (res1.direction == Buy && res2.direction == Buy);
                bool trendSignalSell = (res1.direction == Sell && res2.direction == Sell);
                if (trendSignalBuy && res3.direction == Buy) finalResult.SetSuccess(Buy);
                else if (trendSignalSell && res3.direction == Sell) finalResult.SetSuccess(Sell);
                else finalResult.SetSuccess(None);
            } else { finalResult.SetError("CFFP_CMSM_AND_CSI: Component signal error. CFFP: "+res1.errorMessage + " CMSM: " + res2.errorMessage + " CSI: " + res3.errorMessage); }
        }
        else // Unknown composite source
        {
            finalResult.SetError(StringFormat("Unhandled composite signal source logic for %s on %s.", EnumToString(compositeSource), forSymbol));
        }
        
        // For composites, primary/supporting values are less direct.
        // Could be count of agreeing signals, or average strength.
        if(finalResult.isValid) {
             finalResult.primaryIndicatorValue = finalResult.direction; // e.g. 2 for Buy, 3 for Sell, 1 for None
        }
        return finalResult;
    }
  };

#endif // ORTBO_CSIGNAL_MANAGER_FULL_MQH
