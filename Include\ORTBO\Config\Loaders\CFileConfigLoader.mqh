//+------------------------------------------------------------------+
//|                                          CFileConfigLoader.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"
#property version   "1.00"

#ifndef ORTBO_CFILE_CONFIG_LOADER_MQH
#define ORTBO_CFILE_CONFIG_LOADER_MQH

#include <Object.mqh>
#include <Files/File.mqh>
#include "../Base/CConfigBase.mqh"
#include "../CConfigFactory.mqh"
#include "../../Enums/ORTBO_Enums.mqh"

//+------------------------------------------------------------------+
//| Configuration loader from files                                 |
//| Supports both text and binary configuration files              |
//+------------------------------------------------------------------+
class CFileConfigLoader : public CObject
  {
private:
   string            m_basePath;       // Base path for configuration files
   
public:
   //--- Constructor
                     CFileConfigLoader(const string basePath = "ORTBO\\Configs\\")
     {
      m_basePath = basePath;
      // Ensure directory exists
      if(!FolderCreate(m_basePath, FILE_COMMON))
        {
         // Directory might already exist, which is fine
        }
     }
   
   //--- Load configuration from text file
   CConfigBase*      LoadFromTextFile(ENUM_SIGNAL_SOURCE source, const string filename)
     {
      string fullPath = m_basePath + filename;
      
      int handle = FileOpen(fullPath, FILE_READ|FILE_TXT|FILE_ANSI|FILE_COMMON);
      if(handle == INVALID_HANDLE)
        {
         PrintFormat("CFileConfigLoader: Failed to open text file '%s'", fullPath);
         return NULL;
        }
      
      string content = "";
      while(!FileIsEnding(handle))
        {
         string line = FileReadString(handle);
         if(content != "") content += "\n";
         content += line;
        }
      FileClose(handle);
      
      // Extract provider-specific configuration
      string providerConfig = ExtractProviderConfig(content, source);
      if(providerConfig == "")
        {
         PrintFormat("CFileConfigLoader: No configuration found for %s in file %s", 
                    EnumToString(source), filename);
         return NULL;
        }
      
      return CConfigFactory::CreateFromString(source, providerConfig);
     }
   
   //--- Load configuration from binary file
   CConfigBase*      LoadFromBinaryFile(ENUM_SIGNAL_SOURCE source, const string filename)
     {
      string fullPath = m_basePath + filename;
      
      int handle = FileOpen(fullPath, FILE_READ|FILE_BIN|FILE_COMMON);
      if(handle == INVALID_HANDLE)
        {
         PrintFormat("CFileConfigLoader: Failed to open binary file '%s'", fullPath);
         return NULL;
        }
      
      CConfigBase* config = CConfigFactory::CreateDefault(source);
      if(config == NULL)
        {
         FileClose(handle);
         return NULL;
        }
      
      // Read configuration type to verify
      string configType = FileReadString(handle);
      if(configType != CConfigFactory::GetExpectedConfigType(source))
        {
         PrintFormat("CFileConfigLoader: Config type mismatch in binary file. Expected %s, got %s", 
                    CConfigFactory::GetExpectedConfigType(source), configType);
         delete config;
         FileClose(handle);
         return NULL;
        }
      
      // Read the configuration data manually since we don't have Deserialize
      // This is a simplified version - in a full implementation you'd need
      // to implement proper binary deserialization for each config type
      string configData = "";
      while(!FileIsEnding(handle))
        {
         configData += FileReadString(handle);
        }

      if(!config.LoadFromString(configData))
        {
         PrintFormat("CFileConfigLoader: Failed to deserialize configuration from binary file");
         delete config;
         FileClose(handle);
         return NULL;
        }
      
      FileClose(handle);
      return config;
     }
   
   //--- Save configuration to text file
   bool              SaveToTextFile(CConfigBase* config, const string filename)
     {
      if(config == NULL) return false;
      
      string fullPath = m_basePath + filename;
      
      int handle = FileOpen(fullPath, FILE_WRITE|FILE_TXT|FILE_ANSI|FILE_COMMON);
      if(handle == INVALID_HANDLE)
        {
         PrintFormat("CFileConfigLoader: Failed to create text file '%s'", fullPath);
         return false;
        }
      
      // Write header
      FileWriteString(handle, "# ORTBO Configuration File");
      FileWriteString(handle, "# Generated: " + TimeToString(TimeCurrent()));
      FileWriteString(handle, "# Config Type: " + config.GetConfigType());
      FileWriteString(handle, "");
      
      // Write configuration section
      FileWriteString(handle, "[" + config.GetConfigType() + "]");
      FileWriteString(handle, config.SaveToString());
      
      FileClose(handle);
      return true;
     }
   
   //--- Save configuration to binary file
   bool              SaveToBinaryFile(CConfigBase* config, const string filename)
     {
      if(config == NULL) return false;
      
      string fullPath = m_basePath + filename;
      
      int handle = FileOpen(fullPath, FILE_WRITE|FILE_BIN|FILE_COMMON);
      if(handle == INVALID_HANDLE)
        {
         PrintFormat("CFileConfigLoader: Failed to create binary file '%s'", fullPath);
         return false;
        }
      
      // Write configuration type for verification
      FileWriteString(handle, config.GetConfigType());
      
      // Write the configuration data manually since we don't have Serialize
      // This is a simplified version - in a full implementation you'd need
      // to implement proper binary serialization for each config type
      string configData = config.SaveToString();
      bool result = (FileWriteString(handle, configData) > 0);
      
      FileClose(handle);
      return result;
     }
   
   //--- Load all configurations from a multi-provider text file
   bool              LoadAllFromTextFile(const string filename,
                                         CConfigBase* &configs[],
                                         ENUM_SIGNAL_SOURCE &sources[],
                                         int &count)
     {
      count = 0;
      string fullPath = m_basePath + filename;
      
      int handle = FileOpen(fullPath, FILE_READ|FILE_TXT|FILE_ANSI|FILE_COMMON);
      if(handle == INVALID_HANDLE)
        {
         PrintFormat("CFileConfigLoader: Failed to open text file '%s'", fullPath);
         return false;
        }
      
      string content = "";
      while(!FileIsEnding(handle))
        {
         string line = FileReadString(handle);
         if(content != "") content += "\n";
         content += line;
        }
      FileClose(handle);
      
      // Parse all provider configurations
      ENUM_SIGNAL_SOURCE allSources[] = {
         SIGNAL_SRC_MA,
         SIGNAL_SRC_RSI,
         SIGNAL_SRC_CSI,
         SIGNAL_SRC_CFFP,
         SIGNAL_SRC_CMSM
      };
      
      int maxSources = ArraySize(allSources);
      ArrayResize(configs, maxSources);
      ArrayResize(sources, maxSources);
      
      for(int i = 0; i < maxSources; i++)
        {
         string providerConfig = ExtractProviderConfig(content, allSources[i]);
         if(providerConfig != "")
           {
            CConfigBase* config = CConfigFactory::CreateFromString(allSources[i], providerConfig);
            if(config != NULL)
              {
               configs[count] = config;
               sources[count] = allSources[i];
               count++;
              }
           }
        }
      
      return count > 0;
     }
   
   //--- Check if file exists
   bool              FileExists(const string filename)
     {
      string fullPath = m_basePath + filename;
      int handle = FileOpen(fullPath, FILE_READ|FILE_COMMON);
      if(handle != INVALID_HANDLE)
        {
         FileClose(handle);
         return true;
        }
      return false;
     }
   
   //--- Get base path
   string            GetBasePath() const { return m_basePath; }
   
   //--- Set base path
   void              SetBasePath(const string basePath) 
     { 
      m_basePath = basePath; 
      if(!FolderCreate(m_basePath, FILE_COMMON))
        {
         // Directory might already exist
        }
     }

private:
   //--- Extract provider-specific configuration from file content
   string            ExtractProviderConfig(const string content, ENUM_SIGNAL_SOURCE source)
     {
      string sectionName = "[" + EnumToString(source) + "]";
      string lines[];
      int lineCount = StringSplit(content, '\n', lines);
      
      bool inSection = false;
      string config = "";
      
      for(int i = 0; i < lineCount; i++)
        {
         string line = lines[i];
         StringTrimLeft(line);
         StringTrimRight(line);
         
         if(StringFind(line, "[") == 0) // New section
           {
            if(inSection) break; // End of our section
            inSection = (line == sectionName);
           }
         else if(inSection && line != "" && StringFind(line, "#") != 0) // Not comment
           {
            config = line; // For single-line configs
            break;
           }
        }
      
      return config;
     }
  };

#endif // ORTBO_CFILE_CONFIG_LOADER_MQH
