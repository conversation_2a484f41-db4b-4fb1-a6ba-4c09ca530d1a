//+------------------------------------------------------------------+
//|                                      SignalIntegration.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"

#ifndef ORTBO_SIGNAL_INTEGRATION_MQH
#define ORTBO_SIGNAL_INTEGRATION_MQH

#include "CSignalManager.mqh"    // The main signal manager
#include "CProviderFactory.mqh"  // The factory to create providers
#include "../Config/CConfigManager.mqh"  // Configuration management
#include "../Config/Loaders/CInputConfigLoader.mqh"  // Input configuration loader
// ORTBO_Enums.mqh is included via CSignalManager -> ISignalProvider -> SignalResult

// --- Note: EA Global Variables ---
// These are needed by ORTBO_InitializeSignalSystem to know which symbols to process.
// They should be defined in ORTBO.mq5 as global variables (not input parameters).
// Expected declarations in ORTBO.mq5:
//   string SymbolArray[];
//   int    NumberOfTradeableSymbols;

// --- Global instances ---
CSignalManager* g_theGlobalSignalManager = NULL; // Global signal manager pointer
CConfigManager* g_theGlobalConfigManager = NULL; // Global configuration manager pointer

//+------------------------------------------------------------------+
//| Initializes the global signal system.                            |
//| Creates CSignalManager and populates it with providers.          |
//+------------------------------------------------------------------+
int ORTBO_InitializeSignalSystem()
  {
   // 1. Create the CConfigManager instance
   if(CheckPointer(g_theGlobalConfigManager) != POINTER_INVALID)
     {
      Print("ORTBO_InitializeSignalSystem Warning: g_theGlobalConfigManager already exists. Deinitializing first.");
      delete g_theGlobalConfigManager;
      g_theGlobalConfigManager = NULL;
     }
   g_theGlobalConfigManager = new CConfigManager();
   if(CheckPointer(g_theGlobalConfigManager) == POINTER_INVALID)
     {
      Print("ORTBO_InitializeSignalSystem Error: Failed to create CConfigManager instance.");
      return INIT_FAILED;
     }

   // Initialize configuration manager
   if(!g_theGlobalConfigManager.Initialize())
     {
      Print("ORTBO_InitializeSignalSystem Error: Failed to initialize CConfigManager.");
      delete g_theGlobalConfigManager;
      g_theGlobalConfigManager = NULL;
      return INIT_FAILED;
     }

   // 2. Create the CSignalManager instance
   if(CheckPointer(g_theGlobalSignalManager) != POINTER_INVALID)
     {
      // Already initialized? Or error? For safety, deinitialize first.
      Print("ORTBO_InitializeSignalSystem Warning: g_theGlobalSignalManager already exists. Deinitializing first.");
      delete g_theGlobalSignalManager;
      g_theGlobalSignalManager = NULL;
     }
   g_theGlobalSignalManager = new CSignalManager();
   if(CheckPointer(g_theGlobalSignalManager) == POINTER_INVALID)
     {
      Print("ORTBO_InitializeSignalSystem Error: Failed to create CSignalManager instance.");
      delete g_theGlobalConfigManager;
      g_theGlobalConfigManager = NULL;
      return INIT_FAILED;
     }

   // 3. Initialize the CSignalManager with the array of symbols from the EA
   if(NumberOfTradeableSymbols <= 0 || ArraySize(SymbolArray) < NumberOfTradeableSymbols)
     {
      Print("ORTBO_InitializeSignalSystem Error: SymbolArray not correctly populated or NumberOfTradeableSymbols is zero.");
      delete g_theGlobalSignalManager;
      g_theGlobalSignalManager = NULL;
      delete g_theGlobalConfigManager;
      g_theGlobalConfigManager = NULL;
      return INIT_FAILED;
     }
   if(!g_theGlobalSignalManager.InitializeManager(SymbolArray, NumberOfTradeableSymbols))
     {
      Print("ORTBO_InitializeSignalSystem Error: CSignalManager.InitializeManager() failed.");
      delete g_theGlobalSignalManager;
      g_theGlobalSignalManager = NULL;
      delete g_theGlobalConfigManager;
      g_theGlobalConfigManager = NULL;
      return INIT_FAILED;
     }

   // 4. Initialize the CProviderFactory with configuration manager
   if(!CProviderFactory::Initialize(g_theGlobalConfigManager))
     {
      Print("ORTBO_InitializeSignalSystem Error: Failed to initialize CProviderFactory with configuration manager.");
      delete g_theGlobalSignalManager;
      g_theGlobalSignalManager = NULL;
      delete g_theGlobalConfigManager;
      g_theGlobalConfigManager = NULL;
      return INIT_FAILED;
     }

   // 5. Create and add all configured signal providers for each symbol
   //    The CProviderFactory now uses the configuration system
   ENUM_SIGNAL_SOURCE basicSources[] = {
      SIGNAL_SRC_MA,
      SIGNAL_SRC_RSI,
      SIGNAL_SRC_CSI,
      SIGNAL_SRC_CFFP,
      SIGNAL_SRC_CMSM
   };
   int numBasicSources = ArraySize(basicSources);

   for(int i = 0; i < NumberOfTradeableSymbols; i++) // For each symbol EA trades
     {
      string currentSymbol = SymbolArray[i];
      if(currentSymbol == "" || currentSymbol == NULL) continue;

      for(int j = 0; j < numBasicSources; j++) // For each basic signal type
        {
         ENUM_SIGNAL_SOURCE srcType = basicSources[j];

         // Use configuration-based provider creation
         CSignalProviderBase* provider = CProviderFactory::CreateProviderInstanceWithConfig(srcType, currentSymbol);

         if(CheckPointer(provider) != POINTER_INVALID)
           {
            // AddProviderForSymbol calls provider.Initialize() internally
            if(!g_theGlobalSignalManager.AddProviderForSymbol(currentSymbol, provider))
              {
               PrintFormat("ORTBO_InitializeSignalSystem Warning: Failed to add provider %s for symbol %s. Provider deleted.",
                           provider.GetProviderName(), currentSymbol);
               // CSignalManager.AddProviderForSymbol should delete the provider if add/init fails.
               // If it doesn't, we would 'delete provider;' here.
               // Based on CSignalManager's AddProviderForSymbol, it does delete on failure.
              }
            // else {
            //    PrintFormat("ORTBO_InitializeSignalSystem: Successfully added provider %s for %s.",
            //                provider.GetProviderName(), currentSymbol);
            // }
           }
         // If provider is NULL, it means it was disabled via EA inputs or failed memory allocation in factory.
         // The factory already prints an error for allocation failure.
        }
     }

   Print("ORTBO_InitializeSignalSystem: Signal system initialization complete.");
   return INIT_SUCCEEDED;
  }

//+------------------------------------------------------------------+
//| Deinitializes the global signal system.                          |
//+------------------------------------------------------------------+
void ORTBO_DeinitializeSignalSystem()
  {
   if(CheckPointer(g_theGlobalSignalManager) != POINTER_INVALID)
     {
      g_theGlobalSignalManager.DeinitializeManager(); // Calls Deinitialize on all providers
      delete g_theGlobalSignalManager;
      g_theGlobalSignalManager = NULL;
     }

   if(CheckPointer(g_theGlobalConfigManager) != POINTER_INVALID)
     {
      g_theGlobalConfigManager.SaveAllDirtyConfigs(); // Save any modified configurations
      delete g_theGlobalConfigManager;
      g_theGlobalConfigManager = NULL;
     }

   // Print("ORTBO_DeinitializeSignalSystem: Signal system and configuration manager deinitialized.");
  }

//+------------------------------------------------------------------+
//| Get global configuration manager instance                        |
//+------------------------------------------------------------------+
CConfigManager* ORTBO_GetConfigManager()
  {
   return g_theGlobalConfigManager;
  }

//+------------------------------------------------------------------+
//| Update configuration for a specific symbol and source           |
//+------------------------------------------------------------------+
bool ORTBO_UpdateConfiguration(const string symbol, ENUM_SIGNAL_SOURCE source, CConfigBase* newConfig)
  {
   if(CheckPointer(g_theGlobalConfigManager) == POINTER_INVALID)
     {
      PrintFormat("ORTBO_UpdateConfiguration: Configuration manager not initialized");
      return false;
     }

   return g_theGlobalConfigManager.SetConfig(symbol, source, newConfig);
  }

//+------------------------------------------------------------------+
//| Save configuration to file                                       |
//+------------------------------------------------------------------+
bool ORTBO_SaveConfiguration(const string symbol, ENUM_SIGNAL_SOURCE source)
  {
   if(CheckPointer(g_theGlobalConfigManager) == POINTER_INVALID)
     {
      PrintFormat("ORTBO_SaveConfiguration: Configuration manager not initialized");
      return false;
     }

   return g_theGlobalConfigManager.SaveConfig(symbol, source);
  }

//+------------------------------------------------------------------+
//| Helper function to determine if a signal source should use the   |
//| new signal system during gradual migration.                      |
//+------------------------------------------------------------------+
bool ORTBO_UseNewSignalSystemForSource(ENUM_SIGNAL_SOURCE source)
  {
   // Initially, enable for basic MA and RSI, and their direct combination.
   // As other providers are tested and stable, add them here.
   switch(source)
     {
      // Basic providers to migrate first
      case SIGNAL_SRC_MA:
      case SIGNAL_SRC_RSI:
      // TODO: Enable these as each provider is implemented and tested
      // case SIGNAL_SRC_CSI:
      // case SIGNAL_SRC_CFFP:
      // case SIGNAL_SRC_CMSM:
         return true;

      // Composite signals involving already migrated basic signals
      case SIGNAL_SRC_MA_AND_RSI:
         return true;
      
      // TODO: Enable other composite signals as their components are migrated
      // case SIGNAL_SRC_MA_AND_CSI:
      //    return ORTBO_UseNewSignalSystemForSource(SIGNAL_SRC_MA) && 
      //           ORTBO_UseNewSignalSystemForSource(SIGNAL_SRC_CSI); 
      // ... and so on for all composite types

      default:
         return false; // Other signals use the old system for now
     }
  }

#endif // ORTBO_SIGNAL_INTEGRATION_MQH
