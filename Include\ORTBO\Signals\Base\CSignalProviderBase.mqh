//+------------------------------------------------------------------+
//|                                       SignalProviderBase.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"

#ifndef ORTBO_SIGNAL_PROVIDER_BASE_MQH
#define ORTBO_SIGNAL_PROVIDER_BASE_MQH

#include <Object.mqh>
#include "ISignalProvider.mqh" 
// SignalResult.mqh and ORTBO_Enums.mqh are included via ISignalProvider.mqh

//+------------------------------------------------------------------+
//| Abstract base class for signal providers.                        |
//| Provides common functionality and state management.              |
//+------------------------------------------------------------------+
class CSignalProviderBase : public CObject
  {
protected:
   string            m_symbol;                     // Symbol this provider instance is initialized for
   // int m_symbolIndex; // symbolIndex from Initialize can be stored if needed by derived classes, not strictly by base
   ENUM_TIMEFRAMES   m_timeframe;                  // Primary timeframe for this provider's calculations
   string            m_providerNameInternal;       // Internal storage for provider name
   bool              m_enabled;                    // Is this provider active?
   int               m_indicatorHandle;            // Handle for the MQL5 indicator
   
   datetime          m_lastSuccessfulCalculationTime; // Timestamp of the last bar (iTime) on m_timeframe for which a valid signal was calculated
   SignalResult      m_lastSignalResult;           // Stores the last calculated signal result (valid or error)

   double            m_indicatorBuffers[];         // Generic buffer for indicator values, resized by derived classes

public:
   //--- Constructor
                     CSignalProviderBase(const string providerName)
                       : m_providerNameInternal(providerName),
                         m_symbol(""),
                         m_timeframe(PERIOD_CURRENT), // Default, should be set by derived class constructor or Initialize
                         m_enabled(true),
                         m_indicatorHandle(INVALID_HANDLE),
                         m_lastSuccessfulCalculationTime(0)
                       {
                        // m_lastSignalResult is default constructed via its own constructor
                        // Provider name is set here, symbol and timeframe in Initialize or derived constructor
                       }

   //--- Destructor
   virtual           ~CSignalProviderBase()
                       {
                        // Ensure resources are released if not already done by an explicit Deinitialize call
                        if(m_indicatorHandle != INVALID_HANDLE)
                          {
                           IndicatorRelease(m_indicatorHandle);
                           m_indicatorHandle = INVALID_HANDLE;
                          }
                       }

//--- ISignalProvider interface method implementations ---
   virtual string    GetProviderName() const { return m_providerNameInternal; }
   virtual bool      IsEnabled() const { return m_enabled; }
   virtual void      SetEnabled(bool enable) { m_enabled = enable; }
   virtual ENUM_TIMEFRAMES GetTimeframe() const { return m_timeframe; }
   
   // Default Type() - derived classes MUST provide a specific ID
   virtual int       Type() const 
                       { 
                        PrintFormat("Warning: Base Type() called for provider %s. Derived class should override.", m_providerNameInternal);
                        return 0; 
                       }

//--- Common Initialization steps
   virtual bool      Initialize(const string forSymbol/*, const int symbolIndex, CParameters* params = NULL*/)
                       {
                        // Deinitialize first to clear any previous state if re-initializing
                        Deinitialize(); 
                        
                        m_symbol = forSymbol;
                        // m_symbolIndex = symbolIndex; // Store if needed
                        m_enabled = true; // Default to enabled on init
                        m_lastSuccessfulCalculationTime = 0;
                        
                        m_lastSignalResult.Reset(); // Reset to default invalid state
                        m_lastSignalResult.symbol = m_symbol; // Pre-fill known parts
                        m_lastSignalResult.providerName = m_providerNameInternal;
                        // m_lastSignalResult.calculatedOnTimeframe will be set by derived class or when timeframe is set

                        // Basic symbol validation
                        if(m_symbol == "")
                          {
                           SetErrorResult("Initialization failed: Symbol cannot be empty.");
                           return false;
                          }
                        if(!SymbolSelect(m_symbol, true))
                          {
                           SetErrorResult(StringFormat("Initialization failed: Could not select symbol %s in Market Watch. Error: %d", m_symbol, GetLastError()));
                           return false;
                          }
                        MqlTick tick; // Check if symbol is available and we can get data
                        if(!SymbolInfoTick(m_symbol, tick))
                          {
                           // This might be a warning rather than a fatal init error for some EAs,
                           // but for a signal provider, it usually means it can't operate.
                           SetErrorResult(StringFormat("Initialization warning: Could not get initial tick for %s. Error: %d. Provider may not function.", m_providerNameInternal, m_symbol, GetLastError()));
                           // Depending on strictness, could return false here.
                          }
                        return true;
                       }

//--- Common Deinitialization steps
   virtual void      Deinitialize()
                       {
                        if(m_indicatorHandle != INVALID_HANDLE)
                          {
                           IndicatorRelease(m_indicatorHandle);
                           m_indicatorHandle = INVALID_HANDLE;
                          }
                        m_enabled = false; // Mark as disabled
                        m_lastSuccessfulCalculationTime = 0;
                        m_lastSignalResult.Reset(); // Clear last result
                        // PrintFormat("%s: Deinitialized for %s.", m_providerNameInternal, m_symbol);
                       }

//--- GetSignal method - Base implementation cannot calculate a signal
   virtual SignalResult GetSignal()
                       {
                        SetErrorResult("Base GetSignal() called. Derived provider must implement signal calculation.");
                        return m_lastSignalResult;
                       }

protected:
   //--- Helper to check for a new bar on the provider's m_timeframe
   //--- Returns true if it's a new bar OR if we haven't calculated for the current bar's open time yet.
   bool              IsNewCalculationNeededForBar()
                       {
                        if(m_symbol == "" || m_timeframe == 0) // PERIOD_CURRENT is 0
                        {
                           // If timeframe is PERIOD_CURRENT, always assume new calculation might be needed (tick-based)
                           // or handle error if symbol/timeframe not properly set.
                           // For simplicity, let's assume if PERIOD_CURRENT, it's always "new" for this check.
                           // More sophisticated logic might be needed for PERIOD_CURRENT based on actual chart ticks.
                           if(m_timeframe == 0) return true; 

                           SetErrorResult("Symbol or timeframe not properly set for IsNewCalculationNeededForBar.");
                           return false; // Indicate an issue
                        }

                        datetime currentIndyBarOpenTime = iTime(m_symbol, m_timeframe, 0);
                        
                        if(currentIndyBarOpenTime == 0) // Error getting current bar time
                        {
                           // Log error, might decide to use cached signal or prevent calculation
                           // SetErrorResult is not called here as this is a helper, caller handles result.
                           PrintFormat("%s: Error getting iTime for %s on %s in IsNewCalculationNeededForBar.", m_providerNameInternal, m_symbol, EnumToString(m_timeframe));
                           return false; // Indicate problem, signal calculation should probably not proceed
                        }
                        
                        // If current bar's open time is later than our last calculation's bar time, it's a new bar.
                        // OR if they are the same, it means we are on the same bar for which we last calculated,
                        // so we might not need to recalculate unless it's the first ever calculation (m_lastSuccessfulCalculationTime == 0)
                        if(currentIndyBarOpenTime > m_lastSuccessfulCalculationTime)
                          {
                           return true; // Definitely a new bar since last successful calculation
                          }
                        // If currentIndyBarOpenTime == m_lastSuccessfulCalculationTime, it means we have already calculated for this bar.
                        // If m_lastSignalResult.timestamp is also >= currentIndyBarOpenTime, we are good.
                        if(currentIndyBarOpenTime == m_lastSuccessfulCalculationTime && m_lastSignalResult.isValid && m_lastSignalResult.timestamp >= currentIndyBarOpenTime)
                          {
                            return false; // Already calculated for this bar, and result is valid
                          }
                        // If we are here, it means:
                        // 1. currentIndyBarOpenTime == m_lastSuccessfulCalculationTime BUT m_lastSignalResult is not valid or timestamp is old
                        // 2. m_lastSuccessfulCalculationTime == 0 (first run)
                        return true; // Needs calculation or recalculation for the current bar
                       }

   //--- Helper to update the last successful calculation timestamp to current bar's open time
   void              UpdateLastSuccessfulCalculationTime()
                       {
                        if(m_symbol == "" || m_timeframe == 0) // PERIOD_CURRENT is 0
                           return;
                        m_lastSuccessfulCalculationTime = iTime(m_symbol, m_timeframe, 0);
                       }

   //--- Helper to populate m_lastSignalResult with error information
   void              SetErrorResult(const string errorMessageText)
                       {
                        m_lastSignalResult.symbol = m_symbol; // Ensure these are set
                        m_lastSignalResult.providerName = m_providerNameInternal;
                        m_lastSignalResult.calculatedOnTimeframe = m_timeframe;
                        m_lastSignalResult.SetError(errorMessageText); 
                       }
                       
   //--- Helper to populate m_lastSignalResult with success information
   void              SetSuccessResult(Direction dir, double str, double indVal, double priceVal)
                       {
                        m_lastSignalResult.symbol = m_symbol;
                        m_lastSignalResult.providerName = m_providerNameInternal;
                        m_lastSignalResult.calculatedOnTimeframe = m_timeframe;
                        m_lastSignalResult.primaryIndicatorValue = indVal;
                        m_lastSignalResult.supportingPriceValue = priceVal;
                        m_lastSignalResult.SetSuccess(dir, str);
                       }

   //--- Helper to safely copy indicator buffer data
   //--- Assumes m_indicatorBuffers has been appropriately sized by the derived class
   bool              CopyIndicatorBufferSafe(const int buffer_num, const int start_pos, const int count)
                       {
                        if(m_indicatorHandle == INVALID_HANDLE)
                          {
                           SetErrorResult(StringFormat("CopyBuffer failed: Indicator handle is invalid for %s", m_providerNameInternal));
                           return false;
                          }
                        if(ArraySize(m_indicatorBuffers) < count)
                          {
                           SetErrorResult(StringFormat("CopyBuffer failed: m_indicatorBuffers too small for %s (need %d, got %d). Derived class must resize.", 
                                                       m_providerNameInternal, count, ArraySize(m_indicatorBuffers)));
                           return false;
                          }

                        ResetLastError();
                        int copied = CopyBuffer(m_indicatorHandle, buffer_num, start_pos, count, m_indicatorBuffers);
                        if(copied != count)
                          {
                           SetErrorResult(StringFormat("CopyBuffer failed for %s. Expected %d, Copied: %d, Error: %d",
                                                      m_providerNameInternal, count, copied, GetLastError()));
                           // Clear buffer to prevent using stale data
                           ArrayInitialize(m_indicatorBuffers, EMPTY_VALUE);
                           return false;
                          }
                        return true;
                       }
  };

#endif // ORTBO_SIGNAL_PROVIDER_BASE_MQH
