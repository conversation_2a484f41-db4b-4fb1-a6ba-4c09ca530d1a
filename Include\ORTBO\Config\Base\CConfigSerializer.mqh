//+------------------------------------------------------------------+
//|                                           CConfigSerializer.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"
#property version   "1.00"

#ifndef ORTBO_CCONFIG_SERIALIZER_MQH
#define ORTBO_CCONFIG_SERIALIZER_MQH

#include <Object.mqh>
#include "CConfigBase.mqh"
#include "../../Enums/ORTBO_Enums.mqh"

//+------------------------------------------------------------------+
//| Configuration serializer for various formats                    |
//| Supports text, binary, and JSON-like serialization             |
//+------------------------------------------------------------------+
class CConfigSerializer : public CObject
  {
public:
   //--- Serialization formats
   enum ENUM_SERIALIZATION_FORMAT
     {
      FORMAT_TEXT,      // Pipe-delimited text
      FORMAT_BINARY,    // Binary format
      FORMAT_INI,       // INI file format
      FORMAT_JSON       // JSON-like format
     };

private:
   ENUM_SERIALIZATION_FORMAT m_format;
   
public:
   //--- Constructor
                     CConfigSerializer(ENUM_SERIALIZATION_FORMAT format = FORMAT_TEXT)
     {
      m_format = format;
     }
   
   //--- Serialize configuration to string
   string            SerializeToString(CConfigBase* config)
     {
      if(config == NULL) return "";
      
      switch(m_format)
        {
         case FORMAT_TEXT:
            return config.SaveToString();
            
         case FORMAT_INI:
            return SerializeToINI(config);
            
         case FORMAT_JSON:
            return SerializeToJSON(config);
            
         default:
            return config.SaveToString();
        }
     }
   
   //--- Deserialize configuration from string
   bool              DeserializeFromString(CConfigBase* config, const string data)
     {
      if(config == NULL || data == "") return false;
      
      switch(m_format)
        {
         case FORMAT_TEXT:
            return config.LoadFromString(data);
            
         case FORMAT_INI:
            return DeserializeFromINI(config, data);
            
         case FORMAT_JSON:
            return DeserializeFromJSON(config, data);
            
         default:
            return config.LoadFromString(data);
        }
     }
   
   //--- Serialize configuration to file
   bool              SerializeToFile(CConfigBase* config, const string filename)
     {
      if(config == NULL) return false;
      
      int handle = FileOpen(filename, FILE_WRITE|FILE_TXT|FILE_ANSI|FILE_COMMON);
      if(handle == INVALID_HANDLE) return false;
      
      string data = SerializeToString(config);
      
      // Write header based on format
      switch(m_format)
        {
         case FORMAT_INI:
            FileWriteString(handle, "# ORTBO Configuration File");
            FileWriteString(handle, "# Format: INI");
            FileWriteString(handle, "# Generated: " + TimeToString(TimeCurrent()));
            FileWriteString(handle, "");
            break;
            
         case FORMAT_JSON:
            FileWriteString(handle, "// ORTBO Configuration File");
            FileWriteString(handle, "// Format: JSON");
            FileWriteString(handle, "// Generated: " + TimeToString(TimeCurrent()));
            break;
        }
      
      FileWriteString(handle, data);
      FileClose(handle);
      return true;
     }
   
   //--- Deserialize configuration from file
   bool              DeserializeFromFile(CConfigBase* config, const string filename)
     {
      if(config == NULL) return false;
      
      int handle = FileOpen(filename, FILE_READ|FILE_TXT|FILE_ANSI|FILE_COMMON);
      if(handle == INVALID_HANDLE) return false;
      
      string content = "";
      while(!FileIsEnding(handle))
        {
         string line = FileReadString(handle);
         if(content != "") content += "\n";
         content += line;
        }
      FileClose(handle);
      
      return DeserializeFromString(config, content);
     }
   
   //--- Serialize multiple configurations to file
   bool              SerializeMultipleToFile(CConfigBase* configs[], 
                                             ENUM_SIGNAL_SOURCE sources[], 
                                             int count, 
                                             const string filename)
     {
      if(count <= 0) return false;
      
      int handle = FileOpen(filename, FILE_WRITE|FILE_TXT|FILE_ANSI|FILE_COMMON);
      if(handle == INVALID_HANDLE) return false;
      
      // Write file header
      FileWriteString(handle, "# ORTBO Multi-Configuration File");
      FileWriteString(handle, "# Generated: " + TimeToString(TimeCurrent()));
      FileWriteString(handle, "# Format: " + EnumToString(m_format));
      FileWriteString(handle, "");
      
      for(int i = 0; i < count; i++)
        {
         if(configs[i] != NULL)
           {
            FileWriteString(handle, "[" + EnumToString(sources[i]) + "]");
            FileWriteString(handle, SerializeToString(configs[i]));
            FileWriteString(handle, "");
           }
        }
      
      FileClose(handle);
      return true;
     }
   
   //--- Get/Set format
   ENUM_SERIALIZATION_FORMAT GetFormat() const { return m_format; }
   void              SetFormat(ENUM_SERIALIZATION_FORMAT format) { m_format = format; }

private:
   //--- Serialize to INI format
   string            SerializeToINI(CConfigBase* config)
     {
      string ini = "[" + config.GetConfigType() + "]\n";
      ini += "ConfigName=" + config.GetConfigName() + "\n";
      ini += "Version=" + IntegerToString(config.GetVersion()) + "\n";
      ini += "LastModified=" + TimeToString(config.GetLastModified()) + "\n";
      ini += "ConfigData=" + config.SaveToString() + "\n";
      return ini;
     }
   
   //--- Deserialize from INI format
   bool              DeserializeFromINI(CConfigBase* config, const string data)
     {
      string lines[];
      int lineCount = StringSplit(data, '\n', lines);
      
      string configData = "";
      for(int i = 0; i < lineCount; i++)
        {
         string line = StringTrimLeft(StringTrimRight(lines[i]));
         if(StringFind(line, "ConfigData=") == 0)
           {
            configData = StringSubstr(line, 11); // Skip "ConfigData="
            break;
           }
        }
      
      return (configData != "") ? config.LoadFromString(configData) : false;
     }
   
   //--- Serialize to JSON format
   string            SerializeToJSON(CConfigBase* config)
     {
      string json = "{\n";
      json += "  \"configType\": \"" + config.GetConfigType() + "\",\n";
      json += "  \"configName\": \"" + config.GetConfigName() + "\",\n";
      json += "  \"version\": " + IntegerToString(config.GetVersion()) + ",\n";
      json += "  \"lastModified\": \"" + TimeToString(config.GetLastModified()) + "\",\n";
      json += "  \"isValid\": " + (config.IsValid() ? "true" : "false") + ",\n";
      json += "  \"configData\": \"" + config.SaveToString() + "\"\n";
      json += "}";
      return json;
     }
   
   //--- Deserialize from JSON format
   bool              DeserializeFromJSON(CConfigBase* config, const string data)
     {
      // Simple JSON parsing for configData field
      int startPos = StringFind(data, "\"configData\": \"");
      if(startPos < 0) return false;
      
      startPos += 15; // Skip "configData": "
      int endPos = StringFind(data, "\"", startPos);
      if(endPos < 0) return false;
      
      string configData = StringSubstr(data, startPos, endPos - startPos);
      return config.LoadFromString(configData);
     }
  };

#endif // ORTBO_CCONFIG_SERIALIZER_MQH
