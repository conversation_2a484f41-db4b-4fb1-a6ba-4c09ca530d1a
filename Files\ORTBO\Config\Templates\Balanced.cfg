# ORTBO Balanced Trading Configuration Template
# This template provides a balance between stability and responsiveness
# Generated: 2025-01-27
# Version: 1.0

[SIGNAL_SRC_MA]
# Format: enabled|timeframe|filterInverter|period|method|appliedPrice|shift|marginPips
# Balanced MA: Medium period, moderate margin for balanced signals
1|15|0|14|1|0|0|6

[SIGNAL_SRC_RSI]
# Format: enabled|timeframe|filterInverter|period|minimum|maximum
# Balanced RSI: Standard period and levels for reliable signals
1|60|0|14|30.0|70.0

[SIGNAL_SRC_CSI]
# Format: enabled|timeframe|filterInverter|maPeriod|maDelta
# Balanced CSI: Standard MA period for balanced response
1|60|0|20|1

[SIGNAL_SRC_CFFP]
# Format: enabled|timeframe|filterInverter|fastMAPeriod|slowMAPeriod|maMethod|appliedPrice
# Balanced CFFP: Moderate spread for balanced trend detection
1|60|0|5|13|0|0

[SIGNAL_SRC_CMSM]
# Format: enabled|timeframe|filterInverter|tradeLevel|timeIndex
# Balanced CMSM: Standard trade level for quality signals
1|60|0|2.0|0

# Configuration Notes:
# - Timeframes balanced between quick response and stability
# - RSI levels at standard 30/70 for reliable overbought/oversold
# - MA periods set to common values (14) for market acceptance
# - CFFP spread moderate for trend confirmation without lag
# - CMSM trade level standard for balanced signal frequency
# - Margin pips moderate for MA to balance noise vs signals
# - EMA method used for MA for good responsiveness
