//+------------------------------------------------------------------+
//|                                         MASignalProvider.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"

#ifndef ORTBO_MA_SIGNAL_PROVIDER_MQH
#define ORTBO_MA_SIGNAL_PROVIDER_MQH

#include "../Base/CSignalProviderBase.mqh"
#include "../ProviderTypeDefinitions.mqh"  // Central provider type definitions
// ORTBO_Enums.mqh is included via CSignalProviderBase -> ISignalProvider -> SignalResult

//+------------------------------------------------------------------+
//| Concrete Moving Average Signal Provider.                         |
//+------------------------------------------------------------------+
class CMASignalProvider : public CSignalProviderBase
  {
private:
   // MA specific parameters
   int               m_maPeriod;
   ENUM_MA_METHOD    m_maMethod;
   ENUM_APPLIED_PRICE m_maAppliedPrice;
   int               m_maShift;
   int               m_maMarginPips;         // Storing margin in pips
   bool              m_maFilterInverter;

public:
   //--- Constructor
                     CMASignalProvider(
                                     const int period,
                                     const ENUM_MA_METHOD method,
                                     const ENUM_APPLIED_PRICE applied_price,
                                     const int shift,
                                     const int margin_pips, // Expect margin in pips from EA inputs
                                     const bool filter_inverter,
                                     const ENUM_TIMEFRAMES signal_timeframe)
                       : CSignalProviderBase("MA") // Call base constructor with provider name
                       {
                        // Store MA specific parameters
                        m_maPeriod = period;
                        m_maMethod = method;
                        m_maAppliedPrice = applied_price;
                        m_maShift = shift;
                        m_maMarginPips = margin_pips;
                        m_maFilterInverter = filter_inverter;
                        
                        // Set the primary timeframe for this provider's calculations (from CSignalProviderBase)
                        m_timeframe = signal_timeframe; 
                       }

   //--- Destructor (base class handles m_indicatorHandle release)
   virtual           ~CMASignalProvider()
                       {
                        // Any CMASignalProvider specific cleanup, if necessary, would go here.
                        // Base class destructor will release m_indicatorHandle if Deinitialize wasn't explicitly called.
                       }

   //--- Initialization
   virtual bool      Initialize(const string forSymbol/*, const int symbolIndex, CParameters* params = NULL*/) override
                       {
                        // Call base class Initialize first. It handles symbol validation, m_symbol assignment, etc.
                        if(!CSignalProviderBase::Initialize(forSymbol))
                           return false; // Base initialization failed
                           
                        // Set the specific timeframe in the inherited m_lastSignalResult structure
                        m_lastSignalResult.calculatedOnTimeframe = m_timeframe;

                        // Create the iMA indicator handle
                        // m_indicatorHandle is inherited from CSignalProviderBase
                        m_indicatorHandle = iMA(m_symbol, m_timeframe, m_maPeriod, m_maShift, m_maMethod, m_maAppliedPrice);

                        if(m_indicatorHandle == INVALID_HANDLE)
                          {
                           // Use SetErrorResult from base class to populate m_lastSignalResult
                           SetErrorResult(StringFormat("Failed to create iMA handle for %s on %s. Period:%d. Error: %d",
                                                       m_symbol, EnumToString(m_timeframe), m_maPeriod, GetLastError()));
                           return false; // Initialization failed
                          }

                        // Resize internal buffer (m_indicatorBuffers from base class) for MA values (we need 1 value for MA)
                        if(ArrayResize(m_indicatorBuffers, 1) != 1)
                          {
                           SetErrorResult(StringFormat("Failed to resize m_indicatorBuffers for MA provider on %s.", m_symbol));
                           // Release handle if buffer fails, as provider is unusable
                           IndicatorRelease(m_indicatorHandle);
                           m_indicatorHandle = INVALID_HANDLE;
                           return false; // Initialization failed
                          }
                        // Set the buffer as series for easy access to [0] for latest complete bar after CopyBuffer(..., 1, 1, ...)
                        ArraySetAsSeries(m_indicatorBuffers, true); 

                        //PrintFormat("%s: Initialized successfully for %s on %s.", GetProviderName(), m_symbol, EnumToString(m_timeframe));
                        return true; // Initialization successful
                       }
                       
   //--- Deinitialization (base class CSignalProviderBase::Deinitialize() handles m_indicatorHandle release)
   // virtual void Deinitialize() override { CSignalProviderBase::Deinitialize(); } // No MA-specific deinit needed beyond base


   //--- Calculate and return the MA signal
   virtual SignalResult GetSignal() override
                       {
                        // 1. Basic Checks (enabled, handle valid, symbol set)
                        if(!m_enabled)
                          {
                           SetErrorResult("Provider is disabled.");
                           return m_lastSignalResult; // Return current m_lastSignalResult (which has error info)
                          }
                        if(m_indicatorHandle == INVALID_HANDLE)
                          {
                           SetErrorResult("MA Indicator handle is invalid.");
                           return m_lastSignalResult;
                          }
                        if(m_symbol == "") // Should have been caught by Initialize, but good check
                          {
                           SetErrorResult("Symbol not set for MA provider.");
                           return m_lastSignalResult;
                          }

                        // 2. "Once per bar" logic for the indicator's timeframe
                        // IsNewCalculationNeededForBar() checks if current bar on m_timeframe is newer than m_lastSuccessfulCalculationTime
                        // OR if the m_lastSignalResult for that bar was invalid.
                        if (!IsNewCalculationNeededForBar())
                          {
                           // Return the previously calculated valid result for the current indicator bar
                           return m_lastSignalResult; 
                          }
                        
                        // If we proceed, it means a new calculation is needed for the current indicator bar.
                        // Reset relevant parts of m_lastSignalResult for a new calculation attempt.
                        // Keep symbol, providerName, calculatedOnTimeframe as they are fixed for this instance.
                        m_lastSignalResult.isValid = false; 
                        m_lastSignalResult.direction = None;
                        m_lastSignalResult.strength = 0.0;
                        m_lastSignalResult.errorMessage = "";
                        m_lastSignalResult.primaryIndicatorValue = EMPTY_VALUE;
                        m_lastSignalResult.supportingPriceValue = EMPTY_VALUE;
                        // Timestamp will be set by SetSuccess/SetError.

                        // 3. --- Actual MA Signal Calculation ---
                        // Get MA value of the previous completed bar (index 1, because buffer is series)
                        if(!CopyIndicatorBufferSafe(0, 1, 1)) // Uses m_indicatorBuffers
                          {
                           // SetErrorResult was called by CopyIndicatorBufferSafe within base class
                           return m_lastSignalResult;
                          }
                        double maValue = m_indicatorBuffers[0];

                        // Get the close price of the previous completed bar on the MA's timeframe
                        double closePrice = iClose(m_symbol, m_timeframe, 1);
                        if(closePrice == 0.0 && TerminalInfoInteger(TERMINAL_CONNECTED)) // iClose returns 0 on error or if no data (check connection)
                          {
                           SetErrorResult(StringFormat("Failed to get previous close price for %s on %s.", m_symbol, EnumToString(m_timeframe)));
                           return m_lastSignalResult;
                          }

                        double point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
                        if(point <= 0)
                          {
                           SetErrorResult(StringFormat("Failed to get valid point value for %s.", m_symbol));
                           return m_lastSignalResult;
                          }

                        Direction signal = None;
                        double marginInPrice = m_maMarginPips * point;

                        // Replicate original ORTBO.mq5 MA logic:
                        // This logic can result in 'None' if price is within the channel, or 'Sell' if both conditions are met due to exact equality.
                        if (closePrice + marginInPrice > maValue)
                           signal = Buy;
                        if (closePrice - marginInPrice < maValue) // This can overwrite Buy if price is exactly on MA and margin is 0
                           signal = Sell;
                           
                        // A more distinct logic to prevent overwrite and handle channel:
                        // if (closePrice > maValue + marginInPrice) signal = Buy;
                        // else if (closePrice < maValue - marginInPrice) signal = Sell;

                        // Apply filter inverter
                        if(m_maFilterInverter)
                          {
                           if(signal == Buy) signal = Sell;
                           else if(signal == Sell) signal = Buy;
                          }
                        
                        // 4. Populate and return the result using base class helpers
                        SetSuccessResult(signal, 100.0, maValue, closePrice); // Default 100% strength for MA
                        
                        // Update the timestamp for "once per bar" logic after a successful calculation for this bar
                        UpdateLastSuccessfulCalculationTime(); 
                           
                        return m_lastSignalResult;
                       }

   //--- Provider Type
   virtual int       Type() const override { return MQL5_PROVIDER_TYPE_ID_MA; }
  };

#endif // ORTBO_MA_SIGNAL_PROVIDER_MQH
