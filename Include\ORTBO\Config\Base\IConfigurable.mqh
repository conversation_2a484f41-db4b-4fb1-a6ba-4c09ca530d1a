//+------------------------------------------------------------------+
//|                                                IConfigurable.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"
#property version   "1.00"

#ifndef ORTBO_ICONFIGURABLE_MQH
#define ORTBO_ICONFIGURABLE_MQH

#include "CConfigBase.mqh"

//+------------------------------------------------------------------+
//| Interface for configurable components                            |
//| Components that can be configured implement this interface      |
//+------------------------------------------------------------------+
interface IConfigurable
  {
   //--- Apply configuration to the component
   //--- Returns true if configuration was successfully applied
   bool ApplyConfig(CConfigBase* config);

   //--- Get current configuration from the component
   //--- Returns a copy of the current configuration
   CConfigBase* GetCurrentConfig();

   //--- Validate configuration before applying
   //--- Returns true if configuration is valid for this component
   bool ValidateConfig(CConfigBase* config);

   //--- Get configuration type that this component accepts
   //--- Returns string identifier for expected config type
   string GetAcceptedConfigType();

   //--- Check if component supports hot reload
   //--- Returns true if configuration can be changed at runtime
   bool SupportsHotReload();

   //--- Notification that configuration has changed
   //--- Called after successful ApplyConfig
   void OnConfigurationChanged();
  };

#endif // ORTBO_ICONFIGURABLE_MQH