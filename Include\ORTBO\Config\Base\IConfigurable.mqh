//+------------------------------------------------------------------+
//|                                                IConfigurable.mqh |
//|                                      Copyright 2025, Your Name   |
//|                                     https://www.yourwebsite.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name"
#property link      "https://www.yourwebsite.com"
#property version   "1.00"

#ifndef ORTBO_ICONFIGURABLE_MQH
#define ORTBO_ICONFIGURABLE_MQH

#include "CConfigBase.mqh"

//+------------------------------------------------------------------+
//| Interface for configurable components                            |
//+------------------------------------------------------------------+
interface IConfigurable
  {
   //--- Apply configuration to the component
   bool ApplyConfig(CConfigBase* config);
   
   //--- Get current configuration
   CConfigBase* GetCurrentConfig();
   
   //--- Validate configuration before applying
   bool ValidateConfig(CConfigBase* config);
   
   //--- Get configuration type identifier
   string GetConfigType();
  };

#endif // ORTBO_ICONFIGURABLE_MQH