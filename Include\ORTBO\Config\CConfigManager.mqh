//+------------------------------------------------------------------+
//|                                             CConfigManager.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"
#property version   "1.00"

#ifndef ORTBO_CCONFIG_MANAGER_MQH
#define ORTBO_CCONFIG_MANAGER_MQH

#include <Object.mqh>
#include <Arrays/ArrayObj.mqh>
#include "Base/CConfigBase.mqh"
#include "CConfigFactory.mqh"
#include "Loaders/CResourceConfigLoader.mqh"
#include "Loaders/CFileConfigLoader.mqh"
#include "Loaders/CChartObjectConfigLoader.mqh"
#include "../Enums/ORTBO_Enums.mqh"

//+------------------------------------------------------------------+
//| Configuration entry for storing configs with metadata           |
//+------------------------------------------------------------------+
class CConfigEntry : public CObject
  {
public:
   ENUM_SIGNAL_SOURCE m_source;
   string            m_symbol;
   CConfigBase*      m_config;
   datetime          m_lastAccessed;
   bool              m_isDirty;        // Needs saving
   
                     CConfigEntry(ENUM_SIGNAL_SOURCE source, const string symbol, CConfigBase* config)
     {
      m_source = source;
      m_symbol = symbol;
      m_config = config;
      m_lastAccessed = TimeCurrent();
      m_isDirty = false;
     }
   
   virtual          ~CConfigEntry()
     {
      if(m_config != NULL)
        {
         delete m_config;
         m_config = NULL;
        }
     }
   
   string            GetKey() const
     {
      return m_symbol + "_" + EnumToString(m_source);
     }
  };

//+------------------------------------------------------------------+
//| Central configuration manager                                   |
//+------------------------------------------------------------------+
class CConfigManager : public CObject
  {
private:
   CArrayObj*        m_configs;        // Array of CConfigEntry objects
   string            m_configPath;     // Base path for config files
   bool              m_autoSave;       // Auto-save dirty configs
   datetime          m_lastCleanup;    // Last cache cleanup time

   // Enhanced loaders from framework patterns
   CFileConfigLoader* m_fileLoader;    // File-based configuration loader
   CChartObjectConfigLoader* m_chartLoader; // Chart object configuration loader
   
   //--- Find configuration entry
   CConfigEntry*     FindEntry(const string symbol, ENUM_SIGNAL_SOURCE source)
     {
      if(m_configs == NULL) return NULL;
      
      string key = symbol + "_" + EnumToString(source);
      for(int i = 0; i < m_configs.Total(); i++)
        {
         CConfigEntry* entry = (CConfigEntry*)m_configs.At(i);
         if(entry != NULL && entry.GetKey() == key)
           {
            entry.m_lastAccessed = TimeCurrent();
            return entry;
           }
        }
      return NULL;
     }
   
   //--- Add new configuration entry
   bool              AddEntry(const string symbol, ENUM_SIGNAL_SOURCE source, CConfigBase* config)
     {
      if(m_configs == NULL || config == NULL) return false;
      
      CConfigEntry* entry = new CConfigEntry(source, symbol, config);
      if(entry == NULL) return false;
      
      return m_configs.Add(entry);
     }

public:
   //--- Constructor
                     CConfigManager() : m_autoSave(true), m_lastCleanup(0)
     {
      m_configs = new CArrayObj();
      if(m_configs != NULL)
         m_configs.FreeMode(true);

      m_configPath = "ORTBO\\Configs\\";

      // Initialize enhanced loaders
      m_fileLoader = new CFileConfigLoader(m_configPath);
      m_chartLoader = new CChartObjectConfigLoader();
     }
   
   //--- Destructor
   virtual          ~CConfigManager()
     {
      if(m_autoSave)
         SaveAllDirtyConfigs();

      if(m_configs != NULL)
        {
         delete m_configs;
         m_configs = NULL;
        }

      if(m_fileLoader != NULL)
        {
         delete m_fileLoader;
         m_fileLoader = NULL;
        }

      if(m_chartLoader != NULL)
        {
         delete m_chartLoader;
         m_chartLoader = NULL;
        }
     }
   
   //--- Initialize manager
   bool              Initialize(const string configPath = "")
     {
      if(configPath != "")
         m_configPath = configPath;
      
      // Ensure config directory exists
      if(!FolderCreate(m_configPath, FILE_COMMON))
        {
         // Directory might already exist, which is fine
        }
      
      return true;
     }
   
   //--- Get configuration for symbol and source
   CConfigBase*      GetConfig(const string symbol, ENUM_SIGNAL_SOURCE source)
     {
      CConfigEntry* entry = FindEntry(symbol, source);
      if(entry != NULL)
         return entry.m_config;
      
      // Try to load from file
      string filename = GetConfigFilename(symbol, source);
      CConfigBase* config = CConfigFactory::CreateFromFile(source, filename);
      
      if(config == NULL)
        {
         // Create from EA inputs as fallback
         config = CConfigFactory::CreateFromInputs(source);
         if(config != NULL)
           {
            config.SetConfigName(symbol + "_" + EnumToString(source));
            // Mark as dirty to save the default config
            if(AddEntry(symbol, source, config))
              {
               CConfigEntry* newEntry = FindEntry(symbol, source);
               if(newEntry != NULL)
                  newEntry.m_isDirty = true;
              }
           }
        }
      else
        {
         // Successfully loaded from file
         AddEntry(symbol, source, config);
        }
      
      return config;
     }
   
   //--- Set configuration for symbol and source
   bool              SetConfig(const string symbol, ENUM_SIGNAL_SOURCE source, CConfigBase* config)
     {
      if(config == NULL) return false;
      
      // Validate configuration type
      if(!CConfigFactory::ValidateConfigType(source, config))
        {
         PrintFormat("CConfigManager: Invalid config type for %s %s", symbol, EnumToString(source));
         return false;
        }
      
      CConfigEntry* entry = FindEntry(symbol, source);
      if(entry != NULL)
        {
         // Replace existing config
         if(entry.m_config != NULL)
            delete entry.m_config;
         entry.m_config = config;
         entry.m_isDirty = true;
        }
      else
        {
         // Add new entry
         if(!AddEntry(symbol, source, config))
            return false;
         
         entry = FindEntry(symbol, source);
         if(entry != NULL)
            entry.m_isDirty = true;
        }
      
      return true;
     }
   
   //--- Save configuration to file
   bool              SaveConfig(const string symbol, ENUM_SIGNAL_SOURCE source)
     {
      CConfigEntry* entry = FindEntry(symbol, source);
      if(entry == NULL || entry.m_config == NULL)
         return false;
      
      string filename = GetConfigFilename(symbol, source);
      if(entry.m_config.SaveToFile(filename))
        {
         entry.m_isDirty = false;
         return true;
        }
      
      return false;
     }
   
   //--- Save all dirty configurations
   void              SaveAllDirtyConfigs()
     {
      if(m_configs == NULL) return;
      
      for(int i = 0; i < m_configs.Total(); i++)
        {
         CConfigEntry* entry = (CConfigEntry*)m_configs.At(i);
         if(entry != NULL && entry.m_isDirty)
           {
            SaveConfig(entry.m_symbol, entry.m_source);
           }
        }
     }
   
   //--- Get configuration filename
   string            GetConfigFilename(const string symbol, ENUM_SIGNAL_SOURCE source)
     {
      return m_configPath + symbol + "_" + EnumToString(source) + ".cfg";
     }
   
   //--- Enable/disable auto-save
   void              SetAutoSave(bool autoSave) { m_autoSave = autoSave; }
   bool              GetAutoSave() const { return m_autoSave; }
   
   //--- Cache management
   void              CleanupCache(int maxAgeMinutes = 60)
     {
      if(m_configs == NULL) return;
      
      datetime cutoffTime = TimeCurrent() - maxAgeMinutes * 60;
      
      for(int i = m_configs.Total() - 1; i >= 0; i--)
        {
         CConfigEntry* entry = (CConfigEntry*)m_configs.At(i);
         if(entry != NULL && entry.m_lastAccessed < cutoffTime)
           {
            if(entry.m_isDirty)
               SaveConfig(entry.m_symbol, entry.m_source);
            
            m_configs.Delete(i);
           }
        }
      
      m_lastCleanup = TimeCurrent();
     }

   //--- Enhanced methods from framework patterns

   //--- Load configuration from template
   CConfigBase*      LoadFromTemplate(const string templateName, ENUM_SIGNAL_SOURCE source)
     {
      return CResourceConfigLoader::LoadTemplate(templateName, source);
     }

   //--- Load all configurations from template
   bool              LoadAllFromTemplate(const string templateName, const string symbol)
     {
      CConfigBase* configs[];
      ENUM_SIGNAL_SOURCE sources[];
      int count;

      if(!CResourceConfigLoader::LoadAllFromTemplate(templateName, configs, sources, count))
         return false;

      bool allSuccess = true;
      for(int i = 0; i < count; i++)
        {
         if(!SetConfig(symbol, sources[i], configs[i]))
           {
            allSuccess = false;
            delete configs[i]; // Clean up if setting failed
           }
        }

      return allSuccess;
     }

   //--- Load configuration from file using file loader
   CConfigBase*      LoadFromFile(ENUM_SIGNAL_SOURCE source, const string filename, bool isBinary = false)
     {
      if(m_fileLoader == NULL) return NULL;

      return isBinary ?
         m_fileLoader.LoadFromBinaryFile(source, filename) :
         m_fileLoader.LoadFromTextFile(source, filename);
     }

   //--- Save configuration to file using file loader
   bool              SaveToFile(CConfigBase* config, const string filename, bool isBinary = false)
     {
      if(m_fileLoader == NULL || config == NULL) return false;

      return isBinary ?
         m_fileLoader.SaveToBinaryFile(config, filename) :
         m_fileLoader.SaveToTextFile(config, filename);
     }

   //--- Load configuration from chart object
   CConfigBase*      LoadFromChartObject(ENUM_SIGNAL_SOURCE source, const string symbol)
     {
      if(m_chartLoader == NULL) return NULL;

      return m_chartLoader.LoadFromChartObject(source, symbol);
     }

   //--- Save configuration to chart object
   bool              SaveToChartObject(CConfigBase* config, ENUM_SIGNAL_SOURCE source, const string symbol)
     {
      if(m_chartLoader == NULL || config == NULL) return false;

      return m_chartLoader.SaveToChartObject(config, source, symbol);
     }

   //--- Get available templates
   void              GetAvailableTemplates(string &templates[])
     {
      CResourceConfigLoader::GetAvailableTemplates(templates);
     }

   //--- Create template summary
   string            CreateTemplateSummary(const string templateName)
     {
      return CResourceConfigLoader::CreateTemplateSummary(templateName);
     }
  };

#endif // ORTBO_CCONFIG_MANAGER_MQH
