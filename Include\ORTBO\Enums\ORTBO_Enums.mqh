//+------------------------------------------------------------------+
//|                                                ORTBO_Enums.mqh |
//|            Enums shared across ORTBO project files               |
//|                          Copyright 2025, Your Name/Company |
//|                                      https://www.example.com |
//+------------------------------------------------------------------+
#ifndef ORTBO_ENUMS_MQH
#define ORTBO_ENUMS_MQH

// --- Core Trade Direction / Signal State ---
// (Moved from GridEngine.mqh)
enum Direction
{
  None = 1, // Signal neutral or inactive / Grid has no direction
  Buy  = 2, // Signal Buy / Grid direction is Buy
  Sell = 3  // Signal Sell / Grid direction is Sell
};

/*
// --- Signal Source Selection (Original from GridEngine.mqh) ---
// This simpler enum is being replaced by ENUM_SIGNAL_SOURCE for GridEngine configuration
// to ensure consistency with ORTBO.mq5 inputs.
// If used elsewhere for a specific, distinct purpose, it should be renamed.
enum SignalSource
{
  SignalNone, // No Signal (renamed from None to avoid conflict if used directly)
  MA,         // Moving Average
  RSI,        // Relative Strength Index
  CSI         // Currency Strength Index
  // If CFFP and CMSM were basic types for this old enum, they would be here.
};
*/

// --- Signal Source Selection (Extended version) ---
// This will now be used by GridEngine as well.
enum ENUM_SIGNAL_SOURCE
{
  SIGNAL_SRC_NONE, 
  SIGNAL_SRC_MA, 
  SIGNAL_SRC_RSI, 
  SIGNAL_SRC_CSI,
  SIGNAL_SRC_CFFP,          // Added for CFFP
  SIGNAL_SRC_CMSM,          // Added for CMSM
  SIGNAL_SRC_MA_AND_RSI, 
  SIGNAL_SRC_MA_AND_CSI,
  SIGNAL_SRC_MA_AND_CFFP, 
  SIGNAL_SRC_RSI_AND_CSI, 
  SIGNAL_SRC_RSI_AND_CFFP,
  SIGNAL_SRC_CSI_AND_CFFP,
  // Combinations
  SIGNAL_SRC_RSI_CSI_AND_CFFP,    // (RSI & CSI) & CFFP
  SIGNAL_SRC_RSI_CSI_AND_CMSM,    // (RSI & CSI) & CMSM
  SIGNAL_SRC_CFFP_CMSM_AND_RSI,   // (CFFP & CMSM) & RSI
  SIGNAL_SRC_CFFP_CMSM_AND_CSI    // (CFFP & CMSM) & CSI
};

// --- Grid Direction Control ---
// (Replaces GridDirectionControl from GridEngine.mqh)
enum ENUM_GRID_DIRECTION
{
  GRID_DIR_BOTH,
  GRID_DIR_BUY_ONLY,
  GRID_DIR_SELL_ONLY
};

// --- Multi-Symbol Mode ---
// (Moved from ORTBO.mq5)
enum MULTISYMBOL
{
  Current,
  All
};

// --- Initial Lot Sizing Mode ---
// (Replaces InitialLotMode from GridEngine.mqh)
enum ENUM_INITIAL_LOT_MODE
{
  FIXED_LOTS,
  PERCENT_RISK
};

// --- Grid Width Calculation Mode ---
// (Replaces GridWidthMode from GridEngine.mqh)
enum ENUM_GRID_WIDTH_MODE
{
  FIXED_POINTS,
  MIN_MAX_LOOKBACK
};

// --- Equity Monitor Magic Filter ---
// (Moved from EquityMonitor.mqh)
enum MagicFilter
{
  All      = 0, // All Trades
  Manual   = 1, // Manually Opened Trades
  Expert   = 2, // EA Opened Trades
  Specific = 3, // Trades Opened With A Specific Magic Number
  Range    = 4  // Trades Opened Within A Magic Number Range
};

// --- Equity Monitor Balance Period ---
// (Moved from EquityMonitor.mqh)
enum BalancePeriod
{
  Daily   = 0, // Daily
  Weekly  = 1, // Weekly
  Monthly = 2, // Monthly
  Custom  = 3  // Custom Start Date/Time
};

// --- Equity Monitor Action ---
// (Moved from EquityMonitor.mqh)
enum EquityMonitorAction
{
  StopTrading,     // Stop Trading Until The Next Period Cycle
  ContinueTrading  // Continue Trading With The Reset Metrics
};

// --- Equity Monitor Limit Hit Reason ---
// (Moved from EquityMonitor.mqh - was a private enum)
enum LimitHitReason
{
  ReasonProfitTarget, // Profit Target Reached
  ReasonLossLimit,    // Loss Limit Reached
  ReasonTrailingProfit// Trailing Profit Stoploss Hit
};

// --- Equity Monitor Logging Level ---
// (Already in your ORTBO_Enums.mqh)
enum ENUM_LOG_LEVEL
{
  LOG_LEVEL_ERROR = 0,
  LOG_LEVEL_INFO  = 1,
  LOG_LEVEL_DEBUG = 2
};

// --- Basket Trail Mode ---
// (Already in your ORTBO_Enums.mqh)
enum ENUM_BASKET_TRAIL_MODE
{
  MODE_ACTUAL_SL,     // Basket trail modifies Stop Loss orders with the broker
  MODE_VIRTUAL_CLOSE  // Basket trail is internal; EA closes all trades when virtual SL is hit
};

#endif // ORTBO_ENUMS_MQH
