//+------------------------------------------------------------------+
//|                                              CConfigFactory.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"
#property version   "1.00"

#ifndef ORTBO_CCONFIG_FACTORY_MQH
#define ORTBO_CCONFIG_FACTORY_MQH

#include "Base/CConfigBase.mqh"
#include "Providers/CMAConfig.mqh"
#include "Providers/CRSIConfig.mqh"
#include "Providers/CCSIConfig.mqh"
#include "Providers/CCFFPConfig.mqh"
#include "Providers/CCMSMConfig.mqh"
#include "../Enums/ORTBO_Enums.mqh"

//+------------------------------------------------------------------+
//| Configuration factory for creating provider configurations      |
//+------------------------------------------------------------------+
class CConfigFactory
  {
public:
   //--- Create configuration from EA input parameters
   static CConfigBase* CreateFromInputs(ENUM_SIGNAL_SOURCE source)
     {
      switch(source)
        {
         case SIGNAL_SRC_MA:
            return new CMAConfig(
               InpMaPeriod,
               InpMaMethod,
               InpMaPrice,
               InpMaShift,
               InpMaMargin,
               InpMaFilterInverter,
               InpMaFrame);
               
         case SIGNAL_SRC_RSI:
            return new CRSIConfig(
               InpRsiPeriod,
               InpRsiMinimum,
               InpRsiMaximum,
               InpRSIFilterInverter,
               InpRSIFrame);
               
         case SIGNAL_SRC_CSI:
            return new CCSIConfig(
               InpCSIMAPeriod,
               InpCSIMADelta,
               InpCSIFilterInverter,
               InpCSIFrame);
               
         case SIGNAL_SRC_CFFP:
            return new CCFFPConfig(
               InpCFFPFastMAPeriod,
               InpCFFPSlowMAPeriod,
               InpCFFPMAMethod,
               InpCFFPAppliedPrice,
               InpCFFPFilterInverter,
               InpCFFPFrame);
               
         case SIGNAL_SRC_CMSM:
            return new CCMSMConfig(
               InpCMSM_TradeLevel,
               InpCMSMTimeFrame,
               InpCMSM_TimeIndex,
               InpCMSMFilterInverter);
               
         default:
            PrintFormat("CConfigFactory: Unknown signal source %s", EnumToString(source));
            return NULL;
        }
     }
   
   //--- Create default configuration
   static CConfigBase* CreateDefault(ENUM_SIGNAL_SOURCE source)
     {
      switch(source)
        {
         case SIGNAL_SRC_MA:
            return new CMAConfig();
            
         case SIGNAL_SRC_RSI:
            return new CRSIConfig();
            
         case SIGNAL_SRC_CSI:
            return new CCSIConfig();
            
         case SIGNAL_SRC_CFFP:
            return new CCFFPConfig();
            
         case SIGNAL_SRC_CMSM:
            return new CCMSMConfig();
            
         default:
            PrintFormat("CConfigFactory: Unknown signal source %s", EnumToString(source));
            return NULL;
        }
     }
   
   //--- Create configuration from string
   static CConfigBase* CreateFromString(ENUM_SIGNAL_SOURCE source, const string configString)
     {
      CConfigBase* config = CreateDefault(source);
      if(config == NULL)
         return NULL;
      
      if(!config.LoadFromString(configString))
        {
         delete config;
         return NULL;
        }
      
      return config;
     }
   
   //--- Create configuration from file
   static CConfigBase* CreateFromFile(ENUM_SIGNAL_SOURCE source, const string filename)
     {
      CConfigBase* config = CreateDefault(source);
      if(config == NULL)
         return NULL;
      
      if(!config.LoadFromFile(filename))
        {
         delete config;
         return NULL;
        }
      
      return config;
     }
   
   //--- Validate configuration type
   static bool ValidateConfigType(ENUM_SIGNAL_SOURCE source, CConfigBase* config)
     {
      if(config == NULL)
         return false;
      
      string expectedType = GetExpectedConfigType(source);
      return (config.GetConfigType() == expectedType);
     }
   
   //--- Get expected configuration type for source
   static string GetExpectedConfigType(ENUM_SIGNAL_SOURCE source)
     {
      switch(source)
        {
         case SIGNAL_SRC_MA:    return "MA";
         case SIGNAL_SRC_RSI:   return "RSI";
         case SIGNAL_SRC_CSI:   return "CSI";
         case SIGNAL_SRC_CFFP:  return "CFFP";
         case SIGNAL_SRC_CMSM:  return "CMSM";
         default:               return "Unknown";
        }
     }
  };

#endif // ORTBO_CCONFIG_FACTORY_MQH
