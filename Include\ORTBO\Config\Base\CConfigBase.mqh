//+------------------------------------------------------------------+
//|                                                 CConfigBase.mqh |
//|                                      Copyright 2025, Your Name   |
//|                                     https://www.yourwebsite.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name"
#property link      "https://www.yourwebsite.com"
#property version   "1.00"

#ifndef ORTBO_CCONFIG_BASE_MQH
#define ORTBO_CCONFIG_BASE_MQH

#include <Object.mqh>

//+------------------------------------------------------------------+
//| Base configuration class                                         |
//+------------------------------------------------------------------+
class CConfigBase : public CObject
  {
protected:
   string            m_configType;        // Configuration type identifier
   string            m_configName;        // Configuration instance name
   datetime          m_lastModified;      // Last modification timestamp
   int               m_version;           // Configuration version
   bool              m_isValid;           // Validation state
   string            m_validationError;   // Last validation error message

public:
   //--- Constructor
                     CConfigBase() : m_configType("Base"),
                                     m_configName("Default"),
                                     m_lastModified(0),
                                     m_version(1),
                                     m_isValid(false),
                                     m_validationError("") {}
   
   //--- Destructor
   virtual          ~CConfigBase() {}
   
   //--- Pure virtual methods to be implemented by derived classes
   virtual bool      Validate() = 0;
   virtual bool      LoadFromString(const string configString) = 0;
   virtual string    SaveToString() const = 0;
   virtual CConfigBase* Clone() const = 0;
   
   //--- Common methods
   virtual bool      LoadFromFile(const string filename)
     {
      int handle = FileOpen(filename, FILE_READ|FILE_TXT|FILE_ANSI);
      if(handle == INVALID_HANDLE)
        {
         m_validationError = "Failed to open file: " + filename;
         return false;
        }
      
      string content = "";
      while(!FileIsEnding(handle))
        {
         content += FileReadString(handle) + "\n";
        }
      FileClose(handle);
      
      return LoadFromString(content);
     }
   
   virtual bool      SaveToFile(const string filename) const
     {
      int handle = FileOpen(filename, FILE_WRITE|FILE_TXT|FILE_ANSI);
      if(handle == INVALID_HANDLE)
         return false;
      
      FileWriteString(handle, SaveToString());
      FileClose(handle);
      return true;
     }
   
   //--- Getters
   string            GetConfigType() const { return m_configType; }
   string            GetConfigName() const { return m_configName; }
   datetime          GetLastModified() const { return m_lastModified; }
   int               GetVersion() const { return m_version; }
   bool              IsValid() const { return m_isValid; }
   string            GetValidationError() const { return m_validationError; }
   
   //--- Setters
   void              SetConfigName(const string name) 
     { 
      m_configName = name; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetVersion(const int version) 
     { 
      m_version = version; 
      m_lastModified = TimeCurrent();
     }
   
protected:
   //--- Helper method for derived classes
   void              SetValidationError(const string error)
     {
      m_validationError = error;
      m_isValid = false;
     }
   
   void              ClearValidationError()
     {
      m_validationError = "";
      m_isValid = true;
     }
  };

#endif // ORTBO_CCONFIG_BASE_MQH