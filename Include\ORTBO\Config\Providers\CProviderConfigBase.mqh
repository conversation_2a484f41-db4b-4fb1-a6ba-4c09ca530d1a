//+------------------------------------------------------------------+
//|                                         CProviderConfigBase.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"
#property version   "1.00"

#ifndef ORTBO_CPROVIDER_CONFIG_BASE_MQH
#define ORTBO_CPROVIDER_CONFIG_BASE_MQH

#include "../Base/CConfigBase.mqh"
#include "../Base/CConfigValidator.mqh"

//+------------------------------------------------------------------+
//| Base configuration class for signal providers                    |
//+------------------------------------------------------------------+
class CProviderConfigBase : public CConfigBase
  {
protected:
   bool              m_enabled;           // Is provider enabled
   ENUM_TIMEFRAMES   m_timeframe;         // Provider timeframe
   bool              m_filterInverter;    // Invert signal logic
   
public:
   //--- Constructor
                     CProviderConfigBase() : m_enabled(true),
                                             m_timeframe(PERIOD_CURRENT),
                                             m_filterInverter(false)
     {
      m_configType = "ProviderBase";
     }
   
   //--- Common validation for all providers
   virtual bool      ValidateCommon(string &error)
     {
      // Validate timeframe
      if(!CConfigValidator::ValidateTimeframe(m_timeframe, error))
         return false;
      
      return true;
     }
   
   //--- Parse common parameters from string
   virtual bool      ParseCommonParams(const string &params[], int &index)
     {
      if(index + 2 >= ArraySize(params))
         return false;
      
      m_enabled = (bool)StringToInteger(params[index++]);
      m_timeframe = (ENUM_TIMEFRAMES)StringToInteger(params[index++]);
      m_filterInverter = (bool)StringToInteger(params[index++]);
      
      return true;
     }
   
   //--- Convert common parameters to string
   virtual string    CommonParamsToString() const
     {
      return StringFormat("%d|%d|%d", m_enabled, m_timeframe, m_filterInverter);
     }
   
   //--- Getters
   bool              IsEnabled() const { return m_enabled; }
   ENUM_TIMEFRAMES   GetTimeframe() const { return m_timeframe; }
   bool              GetFilterInverter() const { return m_filterInverter; }
   
   //--- Setters
   void              SetEnabled(bool enabled) 
     { 
      m_enabled = enabled; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetTimeframe(ENUM_TIMEFRAMES timeframe) 
     { 
      m_timeframe = timeframe; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetFilterInverter(bool invert)
     {
      m_filterInverter = invert;
      m_lastModified = TimeCurrent();
     }

   //--- Serialization support for binary files
   virtual bool      Serialize(int handle)
     {
      if(handle == INVALID_HANDLE) return false;

      FileWriteInteger(handle, m_enabled ? 1 : 0);
      FileWriteInteger(handle, (int)m_timeframe);
      FileWriteInteger(handle, m_filterInverter ? 1 : 0);
      FileWriteLong(handle, m_lastModified);
      FileWriteInteger(handle, m_version);

      return true;
     }

   virtual bool      Deserialize(int handle)
     {
      if(handle == INVALID_HANDLE) return false;

      m_enabled = FileReadInteger(handle) != 0;
      m_timeframe = (ENUM_TIMEFRAMES)FileReadInteger(handle);
      m_filterInverter = FileReadInteger(handle) != 0;
      m_lastModified = FileReadLong(handle);
      m_version = FileReadInteger(handle);

      string error;
      return ValidateCommon(error);
     }

   //--- Enhanced features from framework patterns

   //--- Cluster-based validation using statistical bounds
   virtual bool      ValidateWithClusterAnalysis(double performanceHistory[], int historySize)
     {
      if(historySize < 3) return Validate(); // Fall back to basic validation

      // Calculate statistical bounds for dynamic validation
      double mean = 0, variance = 0;
      for(int i = 0; i < historySize; i++)
        {
         mean += performanceHistory[i];
        }
      mean /= historySize;

      for(int i = 0; i < historySize; i++)
        {
         variance += MathPow(performanceHistory[i] - mean, 2);
        }
      variance /= (historySize - 1);

      double stdDev = MathSqrt(variance);
      double lowerBound = mean - 2 * stdDev;
      double upperBound = mean + 2 * stdDev;

      // Use statistical bounds to validate current configuration effectiveness
      return (mean > lowerBound && GetOptimizationScore() > lowerBound);
     }

   //--- Support for incremental optimization updates
   virtual void      UpdateFromOptimization(double performance)
     {
      // Base implementation - derived classes can override for specific optimizations
      m_lastModified = TimeCurrent();

      // Store performance for future cluster analysis
      StorePerformanceMetric(performance);
     }

   //--- Get optimization score for this configuration
   virtual double    GetOptimizationScore() const
     {
      // Base implementation returns neutral score
      // Derived classes should implement specific scoring logic
      return 0.5; // Neutral score
     }

   //--- Store performance metric for cluster analysis
   virtual void      StorePerformanceMetric(double performance)
     {
      // Base implementation - could be enhanced with persistent storage
      // For now, just update the modification time
      m_lastModified = TimeCurrent();
     }
  };

#endif // ORTBO_CPROVIDER_CONFIG_BASE_MQH