//+------------------------------------------------------------------+
//|                                                    CMAConfig.mqh |
//|                                      Copyright 2025, Your Name   |
//|                                     https://www.yourwebsite.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name"
#property link      "https://www.yourwebsite.com"
#property version   "1.00"

#ifndef ORTBO_CMA_CONFIG_MQH
#define ORTBO_CMA_CONFIG_MQH

#include "CProviderConfigBase.mqh"

//+------------------------------------------------------------------+
//| Moving Average provider configuration                            |
//+------------------------------------------------------------------+
class CMAConfig : public CProviderConfigBase
  {
private:
   int               m_period;            // MA period
   ENUM_MA_METHOD    m_method;            // MA method
   ENUM_APPLIED_PRICE m_appliedPrice;     // Applied price
   int               m_shift;             // MA shift
   int               m_marginPips;        // Margin in pips
   
public:
   //--- Constructor with default values
                     CMAConfig() : m_period(3),
                                   m_method(MODE_EMA),
                                   m_appliedPrice(PRICE_CLOSE),
                                   m_shift(0),
                                   m_marginPips(6)
     {
      m_configType = "MA";
     }
   
   //--- Constructor with parameters
                     CMAConfig(int period,
                               ENUM_MA_METHOD method,
                               ENUM_APPLIED_PRICE appliedPrice,
                               int shift,
                               int marginPips,
                               bool filterInverter,
                               ENUM_TIMEFRAMES timeframe)
     {
      m_configType = "MA";
      m_period = period;
      m_method = method;
      m_appliedPrice = appliedPrice;
      m_shift = shift;
      m_marginPips = marginPips;
      m_filterInverter = filterInverter;
      m_timeframe = timeframe;
      m_enabled = true;
     }
   
   //--- Validation
   virtual bool      Validate() override
     {
      string error;
      
      // Validate common parameters
      if(!ValidateCommon(error))
        {
         SetValidationError(error);
         return false;
        }
      
      // Validate MA period
      if(!CConfigValidator::ValidateIntRange(m_period, 1, 1000, error))
        {
         SetValidationError("MA Period: " + error);
         return false;
        }
      
      // Validate MA method
      if(!CConfigValidator::ValidateMAMethod(m_method, error))
        {
         SetValidationError("MA Method: " + error);
         return false;
        }
      
      // Validate applied price
      if(!CConfigValidator::ValidateAppliedPrice(m_appliedPrice, error))
        {
         SetValidationError("Applied Price: " + error);
         return false;
        }
      
      // Validate shift
      if(!CConfigValidator::ValidateIntRange(m_shift, 0, 100, error))
        {
         SetValidationError("MA Shift: " + error);
         return false;
        }
      
      // Validate margin pips
      if(!CConfigValidator::ValidateIntRange(m_marginPips, 0, 1000, error))
        {
         SetValidationError("Margin Pips: " + error);
         return false;
        }
      
      ClearValidationError();
      return true;
     }
   
   //--- Serialization
   virtual bool      LoadFromString(const string configString) override
     {
      string params[];
      int count = StringSplit(configString, '|', params);
      
      if(count < 8)
        {
         SetValidationError("Invalid configuration string format");
         return false;
        }
      
      int index = 0;
      
      // Parse common parameters
      if(!ParseCommonParams(params, index))
        {
         SetValidationError("Failed to parse common parameters");
         return false;
        }
      
      // Parse MA-specific parameters
      m_period = (int)StringToInteger(params[index++]);
      m_method = (ENUM_MA_METHOD)StringToInteger(params[index++]);
      m_appliedPrice = (ENUM_APPLIED_PRICE)StringToInteger(params[index++]);
      m_shift = (int)StringToInteger(params[index++]);
      m_marginPips = (int)StringToInteger(params[index++]);
      
      return Validate();
     }
   
   virtual string    SaveToString() const override
     {
      string common = CommonParamsToString();
      string specific = StringFormat("%d|%d|%d|%d|%d",
                                     m_period, m_method, m_appliedPrice, 
                                     m_shift, m_marginPips);
      return common + "|" + specific;
     }
   
   //--- Clone
   virtual CConfigBase* Clone() const override
     {
      CMAConfig* clone = new CMAConfig();
      clone.m_enabled = m_enabled;
      clone.m_timeframe = m_timeframe;
      clone.m_filterInverter = m_filterInverter;
      clone.m_period = m_period;
      clone.m_method = m_method;
      clone.m_appliedPrice = m_appliedPrice;
      clone.m_shift = m_shift;
      clone.m_marginPips = m_marginPips;
      clone.m_configName = m_configName;
      clone.m_version = m_version;
      return clone;
     }
   
   //--- Getters
   int               GetPeriod() const { return m_period; }
   ENUM_MA_METHOD    GetMethod() const { return m_method; }
   ENUM_APPLIED_PRICE GetAppliedPrice() const { return m_appliedPrice; }
   int               GetShift() const { return m_shift; }
   int               GetMarginPips() const { return m_marginPips; }
   
   //--- Setters
   void              SetPeriod(int period) 
     { 
      m_period = period; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetMethod(ENUM_MA_METHOD method) 
     { 
      m_method = method; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetAppliedPrice(ENUM_APPLIED_PRICE price) 
     { 
      m_appliedPrice = price; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetShift(int shift) 
     { 
      m_shift = shift; 
      m_lastModified = TimeCurrent();
     }
   
   void              SetMarginPips(int pips) 
     { 
      m_marginPips = pips; 
      m_lastModified = TimeCurrent();
     }
  };

#endif // ORTBO_CMA_CONFIG_MQH