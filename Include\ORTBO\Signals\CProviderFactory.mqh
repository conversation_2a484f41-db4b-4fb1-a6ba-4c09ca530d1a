//+------------------------------------------------------------------+
//|                                         CProviderFactory.mqh |
//|                   Copyright 2025, Your Name (philuk)             |
//|                                      mailto:<EMAIL>   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name (philuk)"
#property link      "mailto:<EMAIL>"

#ifndef ORTBO_CPROVIDER_FACTORY_MQH
#define ORTBO_CPROVIDER_FACTORY_MQH

#include "Base/CSignalProviderBase.mqh"
#include "Providers/CMASignalProvider.mqh"
#include "Providers/CRSISignalProvider.mqh"
#include "Providers/CCSISignalProvider.mqh"
#include "Providers/CCFFPSignalProvider.mqh"
#include "Providers/CCMSMSignalProvider.mqh"
#include "../Config/CConfigManager.mqh"
#include "../Config/Loaders/CInputConfigLoader.mqh"
// ORTBO_Enums.mqh is included via ISignalProvider -> SignalResult

// --- Note: EA global input parameters ---
// The factory accesses the EA's input parameters directly.
// These parameters should be declared as 'input' in the main EA file (ORTBO.mq5).
// This is a temporary measure for Phase 2. In Phase 3 (Configuration System),
// parameters would ideally come from dedicated configuration objects.
//
// Expected input parameters in ORTBO.mq5:
// MA Parameters:
//   input bool             InpEnableSignalMovingAverage
//   input ENUM_TIMEFRAMES  InpMaFrame
//   input int              InpMaPeriod
//   input ENUM_MA_METHOD   InpMaMethod
//   input ENUM_APPLIED_PRICE InpMaPrice
//   input int              InpMaShift
//   input int              InpMaMargin
//   input bool             InpMaFilterInverter
//
// RSI Parameters:
//   input bool             InpEnableSignalRSI
//   input ENUM_TIMEFRAMES  InpRSIFrame
//   input int              InpRsiPeriod
//   input double           InpRsiMinimum
//   input double           InpRsiMaximum
//   input bool             InpRSIFilterInverter
//
// CSI Parameters:
//   input bool             InpEnableSignalCSI
//   input ENUM_TIMEFRAMES  InpCSIFrame
//   input int              InpCSIMAPeriod
//   input int              InpCSIMADelta
//   input bool             InpCSIFilterInverter
//
// CFFP Parameters:
//   input bool             InpEnableSignalCFFP
//   input ENUM_TIMEFRAMES  InpCFFPFrame
//   input int              InpCFFPFastMAPeriod
//   input int              InpCFFPSlowMAPeriod
//   input ENUM_MA_METHOD   InpCFFPMAMethod
//   input ENUM_APPLIED_PRICE InpCFFPAppliedPrice
//   input bool             InpCFFPFilterInverter
//
// CMSM Parameters:
//   input bool             InpEnableSignalCMSM
//   input ENUM_TIMEFRAMES  InpCMSMTimeFrame
//   input double           InpCMSM_TradeLevel
//   input int              InpCMSM_TimeIndex
//   input bool             InpCMSMFilterInverter


//+------------------------------------------------------------------+
//| CProviderFactory: Creates instances of signal providers.         |
//| Uses static methods as it doesn't need to maintain state.        |
//+------------------------------------------------------------------+
class CProviderFactory // Does not need to inherit CObject if only static methods
  {
private:
   static CConfigManager* s_configManager; // Static config manager instance

public:
   //--- Initialize factory with configuration manager
   static bool Initialize(CConfigManager* configManager)
     {
      s_configManager = configManager;
      return (s_configManager != NULL);
     }

   //--- Creates a signal provider instance using configuration system
   //--- Returns CSignalProviderBase* or NULL if creation fails or source is disabled
   static CSignalProviderBase* CreateProviderInstanceWithConfig(
                                    const ENUM_SIGNAL_SOURCE signalSource,
                                    const string forSymbol)
     {
      if(s_configManager == NULL)
        {
         PrintFormat("CProviderFactory: Configuration manager not initialized. Using legacy method.");
         return CreateProviderInstance(signalSource, forSymbol);
        }

      // Get configuration for this symbol and source
      CConfigBase* config = s_configManager.GetConfig(forSymbol, signalSource);
      if(config == NULL)
        {
         PrintFormat("CProviderFactory: No configuration found for %s %s", forSymbol, EnumToString(signalSource));
         return NULL;
        }

      // Check if provider is enabled
      CProviderConfigBase* providerConfig = dynamic_cast<CProviderConfigBase*>(config);
      if(providerConfig == NULL || !providerConfig.IsEnabled())
        {
         return NULL; // Provider disabled or invalid config type
        }

      return CreateProviderFromConfig(signalSource, providerConfig);
     }

   //--- Creates a signal provider instance based on the source type and EA inputs (legacy method)
   //--- Returns CSignalProviderBase* or NULL if creation fails or source is disabled.
   //--- The caller (e.g., SignalIntegration or CSignalManager) is responsible for
   //--- calling Initialize() on the returned provider and managing its lifetime if AddProvider fails.
   static CSignalProviderBase* CreateProviderInstance(
                                    const ENUM_SIGNAL_SOURCE signalSource,
                                    const string forSymbol // The symbol this provider instance will be for
                                    // No need to pass symbolIndex here, Initialize will get it if needed
                                    )
     {
      CSignalProviderBase* provider = NULL;

      switch(signalSource)
        {
         case SIGNAL_SRC_MA:
            if(InpEnableSignalMovingAverage) // Check global enable flag from EA inputs
              {
               provider = new CMASignalProvider(
                  InpMaPeriod,
                  InpMaMethod,
                  InpMaPrice,
                  InpMaShift,
                  InpMaMargin, // Margin in pips
                  InpMaFilterInverter,
                  InpMaFrame);
              }
            break;

         case SIGNAL_SRC_RSI:
            if(InpEnableSignalRSI)
              {
               provider = new CRSISignalProvider(
                  InpRsiPeriod,
                  InpRsiMinimum,
                  InpRsiMaximum,
                  InpRSIFilterInverter,
                  InpRSIFrame);
              }
            break;

         case SIGNAL_SRC_CSI:
            if(InpEnableSignalCSI)
              {
               provider = new CCSISignalProvider(
                  InpCSIMAPeriod,
                  InpCSIMADelta,
                  InpCSIFilterInverter,
                  InpCSIFrame);
              }
            break;

         case SIGNAL_SRC_CFFP:
            if(InpEnableSignalCFFP)
              {
               provider = new CCFFPSignalProvider(
                  InpCFFPFastMAPeriod,
                  InpCFFPSlowMAPeriod,
                  InpCFFPMAMethod,
                  InpCFFPAppliedPrice,
                  InpCFFPFilterInverter,
                  InpCFFPFrame);
              }
            break;

         case SIGNAL_SRC_CMSM:
            if(InpEnableSignalCMSM)
              {
               provider = new CCMSMSignalProvider(
                  InpCMSM_TradeLevel,
                  InpCMSMTimeFrame, // This is the timeframe for the CMSM indicator
                  InpCMSM_TimeIndex,
                  // ... Pass ALL other parameters your CMSM iCustom call needs ...
                  InpCMSMFilterInverter
                  // The last parameter to CCMSMSignalProvider constructor is 'signalTimeframe',
                  // which is InpCMSMTimeFrame for this provider.
                  // Corrected constructor call based on CCMSMSignalProvider:
                  // CCMSMSignalProvider(tradeLevel, indicatorTimeframe, timeIndex, filterInverter)
                  // Assuming the last param of constructor was 'signalTimeframe' which is same as 'indicatorTimeframe' for CMSM
                  // The CCMSMSignalProvider constructor is:
                  // CCMSMSignalProvider(tradeLevelParam, indicatorTimeframe, timeIndexParam, filterInverter)
                  // The last parameter of the constructor was intended to be the overall signal timeframe,
                  // which for CMSM is its own indicatorTimeframe.
                  // So, the call should be:
                  // provider = new CCMSMSignalProvider(
                  //    InpCMSM_TradeLevel,
                  //    InpCMSMTimeFrame, // This is the indicator's operating TF
                  //    InpCMSM_TimeIndex,
                  //    // ... other custom params ...
                  //    InpCMSMFilterInverter
                  // );
                  // The constructor of CCMSMSignalProvider sets its internal m_timeframe (from CSignalProviderBase)
                  // to the indicatorTimeframe passed to it.
                  // Let's re-verify CCMSMSignalProvider constructor:
                  // CCMSMSignalProvider(tradeLevel, indicatorTF_for_iCustom, timeIndex, filterInverter)
                  // The last param 'signalTimeframe' in my previous thought was for the provider's own update logic,
                  // which for CMSM is tied to its indicator's timeframe.
                  // The CCMSMSignalProvider constructor takes:
                  // (tradeLevelParam, indicatorTimeframe (for iCustom), timeIndexParam, filterInverter)
                  // and internally sets this.m_timeframe = indicatorTimeframe. This is correct.
                  );
              }
            break;

         default:
            // This factory only creates basic providers. Composite signals are handled by CSignalManager.
            // PrintFormat("CProviderFactory: Signal source %s is composite or unknown for basic provider creation.", EnumToString(signalSource));
            break;
        }

      if(CheckPointer(provider) == POINTER_INVALID &&
         ((signalSource == SIGNAL_SRC_MA && InpEnableSignalMovingAverage) ||
          (signalSource == SIGNAL_SRC_RSI && InpEnableSignalRSI) ||
          (signalSource == SIGNAL_SRC_CSI && InpEnableSignalCSI) ||
          (signalSource == SIGNAL_SRC_CFFP && InpEnableSignalCFFP) ||
          (signalSource == SIGNAL_SRC_CMSM && InpEnableSignalCMSM)) )
        {
         // If it was supposed to be created (enabled) but 'new' failed
         PrintFormat("CProviderFactory Error: Failed to allocate memory for provider %s for symbol %s.", EnumToString(signalSource), forSymbol);
        }
        
      // The caller (CSignalManager via SignalIntegration) will call Initialize() on the provider.
      return provider;
     }

private:
   //--- Create provider from configuration object
   static CSignalProviderBase* CreateProviderFromConfig(ENUM_SIGNAL_SOURCE signalSource, CProviderConfigBase* config)
     {
      if(config == NULL) return NULL;

      CSignalProviderBase* provider = NULL;

      switch(signalSource)
        {
         case SIGNAL_SRC_MA:
           {
            CMAConfig* maConfig = dynamic_cast<CMAConfig*>(config);
            if(maConfig != NULL)
              {
               provider = new CMASignalProvider(
                  maConfig.GetPeriod(),
                  maConfig.GetMethod(),
                  maConfig.GetAppliedPrice(),
                  maConfig.GetShift(),
                  maConfig.GetMarginPips(),
                  maConfig.GetFilterInverter(),
                  maConfig.GetTimeframe());
              }
            break;
           }

         case SIGNAL_SRC_RSI:
           {
            CRSIConfig* rsiConfig = dynamic_cast<CRSIConfig*>(config);
            if(rsiConfig != NULL)
              {
               provider = new CRSISignalProvider(
                  rsiConfig.GetPeriod(),
                  rsiConfig.GetMinimum(),
                  rsiConfig.GetMaximum(),
                  rsiConfig.GetFilterInverter(),
                  rsiConfig.GetTimeframe());
              }
            break;
           }

         case SIGNAL_SRC_CSI:
           {
            CCSIConfig* csiConfig = dynamic_cast<CCSIConfig*>(config);
            if(csiConfig != NULL)
              {
               provider = new CCSISignalProvider(
                  csiConfig.GetMAPeriod(),
                  csiConfig.GetMADelta(),
                  csiConfig.GetFilterInverter(),
                  csiConfig.GetTimeframe());
              }
            break;
           }

         case SIGNAL_SRC_CFFP:
           {
            CCFFPConfig* cffpConfig = dynamic_cast<CCFFPConfig*>(config);
            if(cffpConfig != NULL)
              {
               provider = new CCFFPSignalProvider(
                  cffpConfig.GetFastMAPeriod(),
                  cffpConfig.GetSlowMAPeriod(),
                  cffpConfig.GetMAMethod(),
                  cffpConfig.GetAppliedPrice(),
                  cffpConfig.GetFilterInverter(),
                  cffpConfig.GetTimeframe());
              }
            break;
           }

         case SIGNAL_SRC_CMSM:
           {
            CCMSMConfig* cmsmConfig = dynamic_cast<CCMSMConfig*>(config);
            if(cmsmConfig != NULL)
              {
               provider = new CCMSMSignalProvider(
                  cmsmConfig.GetTradeLevel(),
                  cmsmConfig.GetTimeframe(),
                  cmsmConfig.GetTimeIndex(),
                  cmsmConfig.GetFilterInverter());
              }
            break;
           }

         default:
            PrintFormat("CProviderFactory: Unknown signal source %s for config-based creation", EnumToString(signalSource));
            break;
        }

      if(provider == NULL)
        {
         PrintFormat("CProviderFactory: Failed to create provider from config for %s", EnumToString(signalSource));
        }

      return provider;
     }
  };

// Static member definition
CConfigManager* CProviderFactory::s_configManager = NULL;

#endif // ORTBO_CPROVIDER_FACTORY_MQH
